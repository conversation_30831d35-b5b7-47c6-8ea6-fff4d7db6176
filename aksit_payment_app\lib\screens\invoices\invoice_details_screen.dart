import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/invoice.dart';
import '../../models/invoice_item.dart';
import '../../models/customer.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/settings_provider.dart';

class InvoiceDetailsScreen extends StatefulWidget {
  final Invoice invoice;

  const InvoiceDetailsScreen({super.key, required this.invoice});

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen> {
  List<InvoiceItem> _items = [];
  Customer? _customer;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerProvider>(context, listen: false);
      
      final items = await invoiceProvider.getInvoiceItems(widget.invoice.id!);
      final customer = await customerProvider.getCustomerById(widget.invoice.customerId);
      
      setState(() {
        _items = items;
        _customer = customer;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('فاتورة ${widget.invoice.invoiceNumber}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareInvoice,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editInvoice();
                  break;
                case 'print':
                  _printInvoice();
                  break;
                case 'delete':
                  _deleteInvoice();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print),
                    SizedBox(width: 8),
                    Text('طباعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات الفاتورة
                  _buildInvoiceHeader(settingsProvider),
                  
                  const SizedBox(height: 24),
                  
                  // معلومات العميل
                  if (_customer != null) _buildCustomerInfo(),
                  
                  const SizedBox(height: 24),
                  
                  // عناصر الفاتورة
                  _buildInvoiceItems(settingsProvider),
                  
                  const SizedBox(height: 24),
                  
                  // ملخص الفاتورة
                  _buildInvoiceSummary(settingsProvider),
                  
                  const SizedBox(height: 24),
                  
                  // معلومات الدفع
                  if (widget.invoice.paymentMethod == PaymentMethod.installment)
                    _buildPaymentInfo(settingsProvider),
                ],
              ),
            ),
    );
  }

  Widget _buildInvoiceHeader(SettingsProvider settingsProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'معلومات الفاتورة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    widget.invoice.statusText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('رقم الفاتورة', widget.invoice.invoiceNumber),
            _buildInfoRow('تاريخ الفاتورة', _formatDate(widget.invoice.invoiceDate)),
            _buildInfoRow('طريقة الدفع', widget.invoice.paymentMethodText),
            
            if (widget.invoice.notes != null && widget.invoice.notes!.isNotEmpty)
              _buildInfoRow('ملاحظات', widget.invoice.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العميل',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('الاسم', _customer!.name),
            _buildInfoRow('الهاتف', _customer!.phone),
            _buildInfoRow('العنوان', _customer!.address),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceItems(SettingsProvider settingsProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'عناصر الفاتورة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            if (_items.isEmpty)
              const Center(
                child: Text('لا توجد عناصر في الفاتورة'),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _items.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final item = _items[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                item.productName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            Text(
                              settingsProvider.formatCurrency(item.totalPrice),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        
                        if (item.productDescription != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            item.productDescription!,
                            style: TextStyle(color: Colors.grey.shade600),
                          ),
                        ],
                        
                        const SizedBox(height: 8),
                        
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'الكمية: ${item.quantity} ${item.unit}',
                                style: TextStyle(color: Colors.grey.shade700),
                              ),
                            ),
                            Expanded(
                              child: Text(
                                'السعر: ${settingsProvider.formatCurrency(item.unitPrice)}',
                                style: TextStyle(color: Colors.grey.shade700),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 4),
                        
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'التكلفة: ${settingsProvider.formatCurrency(item.totalCost)}',
                                style: TextStyle(color: Colors.grey.shade700),
                              ),
                            ),
                            Expanded(
                              child: Text(
                                'الربح: ${settingsProvider.formatCurrency(item.profit)} (${item.profitPercentage.toStringAsFixed(1)}%)',
                                style: TextStyle(
                                  color: item.profit >= 0 ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceSummary(SettingsProvider settingsProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الفاتورة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildSummaryRow(
              'إجمالي التكلفة',
              settingsProvider.formatCurrency(widget.invoice.costAmount),
              Colors.orange,
            ),
            _buildSummaryRow(
              'إجمالي المبيعات',
              settingsProvider.formatCurrency(widget.invoice.totalAmount),
              Colors.blue,
            ),
            _buildSummaryRow(
              'إجمالي الربح',
              settingsProvider.formatCurrency(widget.invoice.profitAmount),
              widget.invoice.profitAmount >= 0 ? Colors.green : Colors.red,
            ),
            _buildSummaryRow(
              'نسبة الربح',
              '${widget.invoice.profitPercentage.toStringAsFixed(1)}%',
              widget.invoice.profitAmount >= 0 ? Colors.green : Colors.red,
            ),
            
            const Divider(),
            
            _buildSummaryRow(
              'المبلغ المدفوع',
              settingsProvider.formatCurrency(widget.invoice.paidAmount),
              Colors.purple,
            ),
            _buildSummaryRow(
              'المبلغ المتبقي',
              settingsProvider.formatCurrency(widget.invoice.remainingAmount),
              widget.invoice.remainingAmount > 0 ? Colors.red : Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo(SettingsProvider settingsProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات التقسيط',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow(
              'عدد الأقساط',
              '${widget.invoice.installmentMonths} شهر',
            ),
            _buildInfoRow(
              'القسط الشهري',
              settingsProvider.formatCurrency(widget.invoice.monthlyInstallment ?? 0),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton.icon(
              onPressed: _viewInstallments,
              icon: const Icon(Icons.schedule),
              label: const Text('عرض جدول الأقساط'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (widget.invoice.status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.active:
        return Colors.blue;
      case InvoiceStatus.completed:
        return Colors.green;
      case InvoiceStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _shareInvoice() {
    // سيتم تطبيقها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة الفاتورة - قيد التطوير')),
    );
  }

  void _editInvoice() {
    // سيتم تطبيقها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل الفاتورة - قيد التطوير')),
    );
  }

  void _printInvoice() {
    // سيتم تطبيقها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة الفاتورة - قيد التطوير')),
    );
  }

  void _deleteInvoice() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف الفاتورة "${widget.invoice.invoiceNumber}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                final invoiceProvider = Provider.of<InvoiceProvider>(
                  context,
                  listen: false,
                );
                
                navigator.pop(); // إغلاق الحوار
                
                final success = await invoiceProvider.deleteInvoice(widget.invoice.id!);

                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        success ? 'تم حذف الفاتورة بنجاح' : 'فشل في حذف الفاتورة',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                  
                  if (success) {
                    navigator.pop(); // العودة إلى الشاشة السابقة
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  void _viewInstallments() {
    // سيتم تطبيقها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض الأقساط - قيد التطوير')),
    );
  }
}
