import 'package:flutter/foundation.dart';
import '../models/payment.dart';
import '../database/payment_dao.dart';

class PaymentProvider with ChangeNotifier {
  final PaymentDao _paymentDao = PaymentDao();
  
  List<Payment> _payments = [];
  List<Map<String, dynamic>> _paymentsWithDetails = [];
  List<Map<String, dynamic>> _todaysPayments = [];
  List<Map<String, dynamic>> _recentPayments = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Payment> get payments => _payments;
  List<Map<String, dynamic>> get paymentsWithDetails => _paymentsWithDetails;
  List<Map<String, dynamic>> get todaysPayments => _todaysPayments;
  List<Map<String, dynamic>> get recentPayments => _recentPayments;
  Map<String, dynamic> get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل جميع المدفوعات
  Future<void> loadPayments() async {
    _setLoading(true);
    try {
      _payments = await _paymentDao.getAllPayments();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل المدفوعات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل مدفوعات اليوم
  Future<void> loadTodaysPayments() async {
    _setLoading(true);
    try {
      _todaysPayments = await _paymentDao.getTodaysPayments();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل مدفوعات اليوم: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل آخر المدفوعات
  Future<void> loadRecentPayments({int limit = 10}) async {
    _setLoading(true);
    try {
      _recentPayments = await _paymentDao.getRecentPayments(limit);
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل آخر المدفوعات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل إحصائيات المدفوعات
  Future<void> loadStatistics() async {
    _setLoading(true);
    try {
      _statistics = await _paymentDao.getPaymentsStatistics();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الإحصائيات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // إضافة دفعة جديدة
  Future<bool> addPayment(Payment payment) async {
    _setLoading(true);
    try {
      final id = await _paymentDao.insertPayment(payment);
      if (id > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في إضافة الدفعة: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث دفعة
  Future<bool> updatePayment(Payment payment) async {
    _setLoading(true);
    try {
      final result = await _paymentDao.updatePayment(payment);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث الدفعة: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف دفعة
  Future<bool> deletePayment(int paymentId) async {
    _setLoading(true);
    try {
      final result = await _paymentDao.deletePayment(paymentId);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في حذف الدفعة: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على مدفوعات قسط معين
  Future<List<Payment>> getPaymentsByInstallmentId(int installmentId) async {
    try {
      return await _paymentDao.getPaymentsByInstallmentId(installmentId);
    } catch (e) {
      _error = 'خطأ في الحصول على مدفوعات القسط: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على مدفوعات عميل معين
  Future<List<Map<String, dynamic>>> getPaymentsByCustomerId(int customerId) async {
    try {
      return await _paymentDao.getPaymentsByCustomerId(customerId);
    } catch (e) {
      _error = 'خطأ في الحصول على مدفوعات العميل: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على المدفوعات في فترة زمنية
  Future<List<Map<String, dynamic>>> getPaymentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _paymentDao.getPaymentsByDateRange(startDate, endDate);
    } catch (e) {
      _error = 'خطأ في الحصول على المدفوعات: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على المدفوعات الشهرية
  Future<List<Map<String, dynamic>>> getMonthlyPayments(int year, int month) async {
    try {
      return await _paymentDao.getMonthlyPayments(year, month);
    } catch (e) {
      _error = 'خطأ في الحصول على المدفوعات الشهرية: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على إجمالي المدفوعات لفترة معينة
  Future<double> getTotalPaymentsForPeriod(DateTime startDate, DateTime endDate) async {
    try {
      return await _paymentDao.getTotalPaymentsForPeriod(startDate, endDate);
    } catch (e) {
      _error = 'خطأ في حساب إجمالي المدفوعات: ${e.toString()}';
      debugPrint(_error);
      return 0.0;
    }
  }

  // البحث في المدفوعات
  Future<List<Map<String, dynamic>>> searchPayments(String query) async {
    try {
      return await _paymentDao.searchPayments(query);
    } catch (e) {
      _error = 'خطأ في البحث: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على دفعة بواسطة ID
  Future<Payment?> getPaymentById(int id) async {
    try {
      return await _paymentDao.getPaymentById(id);
    } catch (e) {
      _error = 'خطأ في الحصول على الدفعة: ${e.toString()}';
      debugPrint(_error);
      return null;
    }
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تحديث البيانات
  Future<void> refresh() async {
    await Future.wait([
      loadPayments(),
      loadTodaysPayments(),
      loadRecentPayments(),
      loadStatistics(),
    ]);
  }
}
