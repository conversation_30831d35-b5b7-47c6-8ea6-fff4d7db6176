import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider with ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  static const String _currencyKey = 'currency';
  static const String _businessNameKey = 'business_name';
  static const String _businessAddressKey = 'business_address';
  static const String _businessPhoneKey = 'business_phone';
  static const String _autoBackupKey = 'auto_backup';
  static const String _backupIntervalKey = 'backup_interval';
  static const String _notificationsKey = 'notifications_enabled';

  ThemeMode _themeMode = ThemeMode.system;
  String _currency = 'IQD';
  String _businessName = 'إدارة الأقساط';
  String _businessAddress = '';
  String _businessPhone = '';
  bool _autoBackupEnabled = true;
  int _backupIntervalDays = 7;
  bool _notificationsEnabled = true;
  bool _isLoading = false;

  // إعدادات إضافية
  bool _isDarkMode = false;
  String _language = 'ar';
  bool _vibrationEnabled = true;
  String _numberFormat = 'comma';
  bool _showCurrencySymbol = true;
  bool _installmentNotifications = true;
  bool _overdueNotifications = true;
  int _reminderTime = 3;
  String _backupFrequency = 'daily';
  bool _backupOnWifiOnly = true;

  // Getters
  ThemeMode get themeMode => _themeMode;
  String get currency => _currency;
  String get businessName => _businessName;
  String get businessAddress => _businessAddress;
  String get businessPhone => _businessPhone;
  bool get autoBackupEnabled => _autoBackupEnabled;

  // Getters للإعدادات الإضافية
  bool get isDarkMode => _isDarkMode;
  String get language => _language;
  bool get vibrationEnabled => _vibrationEnabled;
  String get numberFormat => _numberFormat;
  bool get showCurrencySymbol => _showCurrencySymbol;
  bool get installmentNotifications => _installmentNotifications;
  bool get overdueNotifications => _overdueNotifications;
  int get reminderTime => _reminderTime;
  String get backupFrequency => _backupFrequency;
  bool get backupOnWifiOnly => _backupOnWifiOnly;
  int get backupIntervalDays => _backupIntervalDays;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get isLoading => _isLoading;

  // تحميل الإعدادات
  Future<void> loadSettings() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل وضع الثيم
      final themeIndex = prefs.getInt(_themeKey) ?? ThemeMode.system.index;
      _themeMode = ThemeMode.values[themeIndex];

      // تحميل العملة
      _currency = prefs.getString(_currencyKey) ?? 'IQD';

      // تحميل بيانات العمل
      _businessName = prefs.getString(_businessNameKey) ?? 'إدارة الأقساط';
      _businessAddress = prefs.getString(_businessAddressKey) ?? '';
      _businessPhone = prefs.getString(_businessPhoneKey) ?? '';

      // تحميل إعدادات النسخ الاحتياطي
      _autoBackupEnabled = prefs.getBool(_autoBackupKey) ?? true;
      _backupIntervalDays = prefs.getInt(_backupIntervalKey) ?? 7;

      // تحميل إعدادات الإشعارات
      _notificationsEnabled = prefs.getBool(_notificationsKey) ?? true;
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تغيير وضع الثيم
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, mode.index);
    } catch (e) {
      debugPrint('خطأ في حفظ وضع الثيم: $e');
    }
  }

  // تغيير العملة
  Future<void> setCurrency(String currency) async {
    _currency = currency;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_currencyKey, currency);
    } catch (e) {
      debugPrint('خطأ في حفظ العملة: $e');
    }
  }

  // تحديث بيانات العمل
  Future<void> updateBusinessInfo({
    String? name,
    String? address,
    String? phone,
  }) async {
    if (name != null) _businessName = name;
    if (address != null) _businessAddress = address;
    if (phone != null) _businessPhone = phone;

    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      if (name != null) await prefs.setString(_businessNameKey, name);
      if (address != null) await prefs.setString(_businessAddressKey, address);
      if (phone != null) await prefs.setString(_businessPhoneKey, phone);
    } catch (e) {
      debugPrint('خطأ في حفظ بيانات العمل: $e');
    }
  }

  // تفعيل/إلغاء النسخ الاحتياطي التلقائي
  Future<void> setAutoBackup(bool enabled) async {
    _autoBackupEnabled = enabled;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoBackupKey, enabled);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات النسخ الاحتياطي: $e');
    }
  }

  // تغيير فترة النسخ الاحتياطي
  Future<void> setBackupInterval(int days) async {
    _backupIntervalDays = days;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_backupIntervalKey, days);
    } catch (e) {
      debugPrint('خطأ في حفظ فترة النسخ الاحتياطي: $e');
    }
  }

  // تفعيل/إلغاء الإشعارات
  Future<void> setNotifications(bool enabled) async {
    _notificationsEnabled = enabled;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_notificationsKey, enabled);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات الإشعارات: $e');
    }
  }

  // إعادة تعيين الإعدادات
  Future<void> resetSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      // إعادة تعيين القيم الافتراضية
      _themeMode = ThemeMode.system;
      _currency = 'IQD';
      _businessName = 'إدارة الأقساط';
      _businessAddress = '';
      _businessPhone = '';
      _autoBackupEnabled = true;
      _backupIntervalDays = 7;
      _notificationsEnabled = true;

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين الإعدادات: $e');
    }
  }

  // الحصول على نص العملة المنسق
  String formatCurrency(double amount) {
    switch (_currency) {
      case 'IQD':
        return '${amount.toStringAsFixed(0)} د.ع';
      case 'USD':
        return '\$${amount.toStringAsFixed(2)}';
      case 'EUR':
        return '€${amount.toStringAsFixed(2)}';
      default:
        return '${amount.toStringAsFixed(2)} $_currency';
    }
  }

  // الحصول على قائمة العملات المدعومة
  List<Map<String, String>> get supportedCurrencies => [
    {'code': 'IQD', 'name': 'الدينار العراقي', 'symbol': 'د.ع'},
    {'code': 'USD', 'name': 'الدولار الأمريكي', 'symbol': '\$'},
    {'code': 'EUR', 'name': 'اليورو', 'symbol': '€'},
  ];

  // الحصول على خيارات فترة النسخ الاحتياطي
  List<Map<String, dynamic>> get backupIntervalOptions => [
    {'days': 1, 'label': 'يومياً'},
    {'days': 3, 'label': 'كل 3 أيام'},
    {'days': 7, 'label': 'أسبوعياً'},
    {'days': 14, 'label': 'كل أسبوعين'},
    {'days': 30, 'label': 'شهرياً'},
  ];

  // Setters للإعدادات الإضافية
  void setDarkMode(bool value) {
    _isDarkMode = value;
    notifyListeners();
  }

  void setLanguage(String value) {
    _language = value;
    notifyListeners();
  }

  void setVibrationEnabled(bool value) {
    _vibrationEnabled = value;
    notifyListeners();
  }

  void setNumberFormat(String value) {
    _numberFormat = value;
    notifyListeners();
  }

  void setShowCurrencySymbol(bool value) {
    _showCurrencySymbol = value;
    notifyListeners();
  }

  void setInstallmentNotifications(bool value) {
    _installmentNotifications = value;
    notifyListeners();
  }

  void setOverdueNotifications(bool value) {
    _overdueNotifications = value;
    notifyListeners();
  }

  void setReminderTime(int value) {
    _reminderTime = value;
    notifyListeners();
  }

  void setBackupFrequency(String value) {
    _backupFrequency = value;
    notifyListeners();
  }

  void setBackupOnWifiOnly(bool value) {
    _backupOnWifiOnly = value;
    notifyListeners();
  }

  void setCurrency(String value) {
    _currency = value;
    notifyListeners();
  }
}
