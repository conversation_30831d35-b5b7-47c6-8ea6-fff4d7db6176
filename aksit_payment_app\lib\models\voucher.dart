enum VoucherType {
  receipt,  // سند قبض
  payment   // سند دفع
}

enum VoucherStatus {
  draft,     // مسودة
  confirmed, // مؤكد
  cancelled  // ملغي
}

class Voucher {
  final int? id;
  final String voucherNumber;
  final VoucherType type;
  final int? customerId;
  final int? invoiceId;
  final double amount;
  final String description;
  final DateTime voucherDate;
  final VoucherStatus status;
  final String? notes;
  final String? attachmentPath;
  final DateTime createdAt;
  final DateTime updatedAt;

  Voucher({
    this.id,
    required this.voucherNumber,
    required this.type,
    this.customerId,
    this.invoiceId,
    required this.amount,
    required this.description,
    required this.voucherDate,
    this.status = VoucherStatus.draft,
    this.notes,
    this.attachmentPath,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى Voucher
  factory Voucher.fromMap(Map<String, dynamic> map) {
    return Voucher(
      id: map['id']?.toInt(),
      voucherNumber: map['voucher_number'] ?? '',
      type: VoucherType.values[map['type'] ?? 0],
      customerId: map['customer_id']?.toInt(),
      invoiceId: map['invoice_id']?.toInt(),
      amount: map['amount']?.toDouble() ?? 0.0,
      description: map['description'] ?? '',
      voucherDate: DateTime.parse(map['voucher_date']),
      status: VoucherStatus.values[map['status'] ?? 0],
      notes: map['notes'],
      attachmentPath: map['attachment_path'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من Voucher إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'voucher_number': voucherNumber,
      'type': type.index,
      'customer_id': customerId,
      'invoice_id': invoiceId,
      'amount': amount,
      'description': description,
      'voucher_date': voucherDate.toIso8601String(),
      'status': status.index,
      'notes': notes,
      'attachment_path': attachmentPath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // الحصول على نص نوع السند
  String get typeText {
    switch (type) {
      case VoucherType.receipt:
        return 'سند قبض';
      case VoucherType.payment:
        return 'سند دفع';
    }
  }

  // الحصول على نص حالة السند
  String get statusText {
    switch (status) {
      case VoucherStatus.draft:
        return 'مسودة';
      case VoucherStatus.confirmed:
        return 'مؤكد';
      case VoucherStatus.cancelled:
        return 'ملغي';
    }
  }

  // الحصول على لون الحالة
  String get statusColor {
    switch (status) {
      case VoucherStatus.draft:
        return 'orange';
      case VoucherStatus.confirmed:
        return 'green';
      case VoucherStatus.cancelled:
        return 'red';
    }
  }

  // الحصول على أيقونة النوع
  String get typeIcon {
    switch (type) {
      case VoucherType.receipt:
        return 'receipt';
      case VoucherType.payment:
        return 'payment';
    }
  }

  // إنشاء نسخة محدثة من السند
  Voucher copyWith({
    int? id,
    String? voucherNumber,
    VoucherType? type,
    int? customerId,
    int? invoiceId,
    double? amount,
    String? description,
    DateTime? voucherDate,
    VoucherStatus? status,
    String? notes,
    String? attachmentPath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Voucher(
      id: id ?? this.id,
      voucherNumber: voucherNumber ?? this.voucherNumber,
      type: type ?? this.type,
      customerId: customerId ?? this.customerId,
      invoiceId: invoiceId ?? this.invoiceId,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      voucherDate: voucherDate ?? this.voucherDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      attachmentPath: attachmentPath ?? this.attachmentPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Voucher(id: $id, voucherNumber: $voucherNumber, type: $type, amount: $amount, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Voucher &&
        other.id == id &&
        other.voucherNumber == voucherNumber &&
        other.type == type &&
        other.amount == amount &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        voucherNumber.hashCode ^
        type.hashCode ^
        amount.hashCode ^
        status.hashCode;
  }
}

// فئة مساعدة لإنشاء أرقام السندات
class VoucherNumberGenerator {
  static String generateReceiptNumber() {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    final time = now.millisecondsSinceEpoch.toString().substring(8);
    
    return 'REC-$year$month$day-$time';
  }

  static String generatePaymentNumber() {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    final time = now.millisecondsSinceEpoch.toString().substring(8);
    
    return 'PAY-$year$month$day-$time';
  }

  static String generateVoucherNumber(VoucherType type) {
    switch (type) {
      case VoucherType.receipt:
        return generateReceiptNumber();
      case VoucherType.payment:
        return generatePaymentNumber();
    }
  }
}

// فئة مساعدة لتصفية السندات
class VoucherFilter {
  final VoucherType? type;
  final VoucherStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? customerId;
  final double? minAmount;
  final double? maxAmount;

  VoucherFilter({
    this.type,
    this.status,
    this.startDate,
    this.endDate,
    this.customerId,
    this.minAmount,
    this.maxAmount,
  });

  bool matches(Voucher voucher) {
    if (type != null && voucher.type != type) return false;
    if (status != null && voucher.status != status) return false;
    if (customerId != null && voucher.customerId != customerId) return false;
    
    if (startDate != null && voucher.voucherDate.isBefore(startDate!)) return false;
    if (endDate != null && voucher.voucherDate.isAfter(endDate!)) return false;
    
    if (minAmount != null && voucher.amount < minAmount!) return false;
    if (maxAmount != null && voucher.amount > maxAmount!) return false;
    
    return true;
  }
}
