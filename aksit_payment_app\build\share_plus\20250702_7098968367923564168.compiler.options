"-Xallow-no-source-files" "-classpath" "D:\\New folder\\aksit\\aksit_payment_app\\build\\share_plus\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b13398fe798413afc9e90906007dadc\\transformed\\jetified-flutter_embedding_debug-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4317354bb0b23ff945f7961364464a69\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2c4a85eef331a72bc176252703ea55a\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142efd8b909bd52f2d0042b95796c3aa\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d5b2d5702492805552364fc43374f12a\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\422ea64cefee41da643bbbfb78c8d64f\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3f173e81a67cfacacac50e6aa230230e\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1d955bc527cdd99ebe33890484a53e4\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9020b0bb04d3436acc6c0105a020a37a\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b30fbdd6f3aa94f92c1e50aad3efea20\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abbb9a8ca8ed0ce257a842bc1a3dd67d\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\afe4f4b2a2d12a5724b5626fce7a33ff\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\496ffad222368f9d1f9b2baaa96f2e69\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f3c7004455fc9c62de36d6727c502fda\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f38d309cef5c24e99252d436f2bc9f27\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2066c8f68bce80528353479346e2d02b\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0a2446df1be4d2941fa9bc64b225a611\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59cd0b4dfef3994a944f8bf632b3c842\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\58ca8f9d4670286800d7b0c293c87aac\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\89ff450dd21663c0b92093067980a3e7\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d01c60684f6d1865530d7fcb1c117a55\\transformed\\jetified-annotation-jvm-1.8.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\33f8de665389b135506e82cdd213b0ec\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f31fea870034967ff4738316a9340e5\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\52c5ca3b247534a5acd5ac1c44c1d0bc\\transformed\\jetified-kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b62cd5bd67c32dcb13ca53167d11276b\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ec6f0a402609a30916a5f8775681be5c\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\512dedc27c3872fc1ae0329026f34c97\\transformed\\jetified-kotlin-stdlib-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2e62f8ae84fc656a2763a8885cdd949f\\transformed\\jetified-kotlin-stdlib-common-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0110fb25dacb611d918be4a17c08643f\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78aac12c79c5be331cd1775be7eaaf20\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5466ab4b956ae678ff71dce115e3d0a\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\43de2aba0ac347139df88f34d7e21c2c\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\New folder\\aksit\\aksit_payment_app\\build\\share_plus\\tmp\\kotlin-classes\\debug" "-jvm-target" "17" "-module-name" "share_plus_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-10.1.4\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\MethodCallHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-10.1.4\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\Share.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-10.1.4\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\ShareFileProvider.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-10.1.4\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\SharePlusPendingIntent.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-10.1.4\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\SharePlusPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-10.1.4\\android\\src\\main\\kotlin\\dev\\fluttercommunity\\plus\\share\\ShareSuccessManager.kt"