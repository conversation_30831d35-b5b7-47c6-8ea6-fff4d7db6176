import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/customer.dart';
import '../../models/invoice.dart';
import '../../models/invoice_item.dart';
import '../../providers/customer_provider.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/settings_provider.dart';

class EditInvoiceScreen extends StatefulWidget {
  final Invoice invoice;

  const EditInvoiceScreen({super.key, required this.invoice});

  @override
  State<EditInvoiceScreen> createState() => _EditInvoiceScreenState();
}

class _EditInvoiceScreenState extends State<EditInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  
  Customer? _selectedCustomer;
  PaymentMethod _paymentMethod = PaymentMethod.cash;
  int? _installmentMonths;
  List<InvoiceItem> _items = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false).loadCustomers();
      _loadInvoiceItems();
    });
  }

  void _initializeData() {
    _paymentMethod = widget.invoice.paymentMethod;
    _installmentMonths = widget.invoice.installmentMonths;
    _notesController.text = widget.invoice.notes ?? '';
  }

  Future<void> _loadInvoiceItems() async {
    final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
    final items = await invoiceProvider.getInvoiceItems(widget.invoice.id!);
    setState(() {
      _items = items;
    });
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تعديل فاتورة ${widget.invoice.invoiceNumber}'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveInvoice,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text(
                    'حفظ',
                    style: TextStyle(color: Colors.white),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الفاتورة الأساسية
              _buildInvoiceInfo(),
              
              const SizedBox(height: 24),
              
              // اختيار العميل
              _buildCustomerSelection(),
              
              const SizedBox(height: 24),
              
              // طريقة الدفع
              _buildPaymentMethodSelection(),
              
              const SizedBox(height: 24),
              
              // عناصر الفاتورة
              _buildInvoiceItems(),
              
              const SizedBox(height: 24),
              
              // ملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
              ),
              
              const SizedBox(height: 24),
              
              // ملخص الفاتورة
              _buildInvoiceSummary(),
              
              const SizedBox(height: 32),
              
              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveInvoice,
                      child: _isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text('حفظ التعديلات'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addInvoiceItem,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildInvoiceInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('رقم الفاتورة', widget.invoice.invoiceNumber),
                ),
                Expanded(
                  child: _buildInfoItem('التاريخ', _formatDate(widget.invoice.invoiceDate)),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('الحالة', widget.invoice.statusText),
                ),
                Expanded(
                  child: _buildInfoItem('طريقة الدفع', widget.invoice.paymentMethodText),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerSelection() {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, child) {
        // العثور على العميل الحالي
        if (_selectedCustomer == null) {
          _selectedCustomer = customerProvider.customers
              .where((c) => c.id == widget.invoice.customerId)
              .firstOrNull;
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العميل *',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<Customer>(
              value: _selectedCustomer,
              decoration: const InputDecoration(
                prefixIcon: Icon(Icons.person),
                hintText: 'اختر العميل',
              ),
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار العميل';
                }
                return null;
              },
              items: customerProvider.customers.map((customer) {
                return DropdownMenuItem<Customer>(
                  value: customer,
                  child: Text('${customer.name} - ${customer.phone}'),
                );
              }).toList(),
              onChanged: (customer) {
                setState(() {
                  _selectedCustomer = customer;
                });
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildPaymentMethodSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'طريقة الدفع',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<PaymentMethod>(
                title: const Text('نقدي'),
                value: PaymentMethod.cash,
                groupValue: _paymentMethod,
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                    _installmentMonths = null;
                  });
                },
              ),
            ),
            Expanded(
              child: RadioListTile<PaymentMethod>(
                title: const Text('تقسيط'),
                value: PaymentMethod.installment,
                groupValue: _paymentMethod,
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                  });
                },
              ),
            ),
          ],
        ),
        
        // عدد الأقساط
        if (_paymentMethod == PaymentMethod.installment) ...[
          const SizedBox(height: 16),
          DropdownButtonFormField<int>(
            value: _installmentMonths,
            decoration: const InputDecoration(
              labelText: 'عدد الأقساط (شهر)',
              prefixIcon: Icon(Icons.schedule),
            ),
            validator: (value) {
              if (_paymentMethod == PaymentMethod.installment && value == null) {
                return 'يرجى اختيار عدد الأقساط';
              }
              return null;
            },
            items: [3, 6, 9, 12, 18, 24, 36].map((months) {
              return DropdownMenuItem<int>(
                value: months,
                child: Text('$months شهر'),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _installmentMonths = value;
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildInvoiceItems() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'عناصر الفاتورة',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            TextButton.icon(
              onPressed: _addInvoiceItem,
              icon: const Icon(Icons.add),
              label: const Text('إضافة عنصر'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        if (_items.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.inventory_2_outlined,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 8),
                Text(
                  'لا توجد عناصر في الفاتورة',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: _addInvoiceItem,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة عنصر'),
                ),
              ],
            ),
          )
        else
          ...List.generate(_items.length, (index) {
            final item = _items[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                title: Text(item.productName),
                subtitle: Text(
                  '${item.quantity} ${item.unit} × ${Provider.of<SettingsProvider>(context).formatCurrency(item.unitPrice)}',
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      Provider.of<SettingsProvider>(context).formatCurrency(item.totalPrice),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _editInvoiceItem(index),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _removeInvoiceItem(index),
                    ),
                  ],
                ),
              ),
            );
          }),
      ],
    );
  }

  Widget _buildInvoiceSummary() {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final totalCost = _items.fold<double>(0, (sum, item) => sum + item.totalCost);
    final totalPrice = _items.fold<double>(0, (sum, item) => sum + item.totalPrice);
    final totalProfit = totalPrice - totalCost;
    final profitPercentage = totalPrice > 0 ? (totalProfit / totalPrice) * 100 : 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الفاتورة',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي التكلفة:'),
                Text(
                  settingsProvider.formatCurrency(totalCost),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي السعر:'),
                Text(
                  settingsProvider.formatCurrency(totalPrice),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي الربح:'),
                Text(
                  settingsProvider.formatCurrency(totalProfit),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: totalProfit >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('نسبة الربح:'),
                Text(
                  '${profitPercentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: profitPercentage >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            
            if (_paymentMethod == PaymentMethod.installment && _installmentMonths != null) ...[
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('القسط الشهري:'),
                  Text(
                    settingsProvider.formatCurrency(totalPrice / _installmentMonths!),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _addInvoiceItem() {
    // سيتم تطبيقها مع نفس dialog من add_invoice_screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة عنصر - قيد التطوير')),
    );
  }

  void _editInvoiceItem(int index) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل العنصر - قيد التطوير')),
    );
  }

  void _removeInvoiceItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) return;
    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة عنصر واحد على الأقل')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
      final totalCost = _items.fold<double>(0, (sum, item) => sum + item.totalCost);
      final totalPrice = _items.fold<double>(0, (sum, item) => sum + item.totalPrice);
      final totalProfit = totalPrice - totalCost;
      
      final updatedInvoice = widget.invoice.copyWith(
        customerId: _selectedCustomer!.id!,
        totalAmount: totalPrice,
        costAmount: totalCost,
        profitAmount: totalProfit,
        remainingAmount: totalPrice - widget.invoice.paidAmount,
        paymentMethod: _paymentMethod,
        installmentMonths: _installmentMonths,
        monthlyInstallment: _paymentMethod == PaymentMethod.installment 
            ? totalPrice / _installmentMonths! 
            : null,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        updatedAt: DateTime.now(),
      );

      final success = await invoiceProvider.updateInvoice(updatedInvoice);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث الفاتورة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في تحديث الفاتورة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
