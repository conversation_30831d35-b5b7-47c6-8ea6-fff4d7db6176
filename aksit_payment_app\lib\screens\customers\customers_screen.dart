import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/customer.dart';
import '../../providers/customer_provider.dart';
import '../../widgets/customer_list_tile.dart';
import 'add_customer_screen.dart';
import 'edit_customer_screen.dart';
import 'customer_details_screen.dart';

class CustomersScreen extends StatefulWidget {
  const CustomersScreen({super.key});

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(
        context,
        listen: false,
      ).loadCustomersWithDebt();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العملاء'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<CustomerProvider>(context, listen: false).refresh();
            },
          ),
        ],
      ),
      body: Consumer<CustomerProvider>(
        builder: (context, customerProvider, child) {
          if (customerProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (customerProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    customerProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      customerProvider.clearError();
                      customerProvider.refresh();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final customersWithDebt = customerProvider.customersWithDebt;

          if (customersWithDebt.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا يوجد عملاء',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ابدأ بإضافة عميل جديد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _addCustomer,
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة عميل'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => customerProvider.refresh(),
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: customersWithDebt.length,
              itemBuilder: (context, index) {
                final customerData = customersWithDebt[index];
                final customer = Customer.fromMap(customerData);

                return CustomerListTile(
                  customer: customer,
                  totalDebt:
                      (customerData['total_debt'] as num?)?.toDouble() ?? 0,
                  totalPaid:
                      (customerData['total_paid'] as num?)?.toDouble() ?? 0,
                  remainingDebt:
                      (customerData['remaining_debt'] as num?)?.toDouble() ?? 0,
                  onTap: () => _viewCustomerDetails(customer),
                  onEdit: () => _editCustomer(customer),
                  onDelete: () => _deleteCustomer(customer),
                );
              },
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addCustomer,
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('البحث عن عميل'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'ادخل اسم العميل أو رقم الهاتف',
              prefixIcon: Icon(Icons.search),
            ),
            autofocus: true,
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _searchController.clear();
                setState(() {
                  _searchQuery = '';
                });
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performSearch();
              },
              child: const Text('بحث'),
            ),
          ],
        );
      },
    );
  }

  void _performSearch() {
    if (_searchQuery.isNotEmpty) {
      Provider.of<CustomerProvider>(
        context,
        listen: false,
      ).searchCustomers(_searchQuery);
    }
  }

  void _addCustomer() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AddCustomerScreen()));
  }

  void _editCustomer(customer) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditCustomerScreen(customer: customer),
      ),
    );
  }

  void _viewCustomerDetails(customer) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CustomerDetailsScreen(customer: customer),
      ),
    );
  }

  void _deleteCustomer(customer) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف العميل "${customer.name}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                final customerProvider = Provider.of<CustomerProvider>(
                  context,
                  listen: false,
                );

                navigator.pop();
                final success = await customerProvider.deleteCustomer(
                  customer.id,
                );

                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        success ? 'تم حذف العميل بنجاح' : 'فشل في حذف العميل',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
