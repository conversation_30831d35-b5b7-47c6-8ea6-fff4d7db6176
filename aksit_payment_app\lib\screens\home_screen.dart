import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/providers.dart';
import '../widgets/dashboard_card.dart';
import 'customers/customers_screen.dart';
import 'invoices/invoices_screen.dart';
import 'vouchers/vouchers_screen.dart';
import 'reports/reports_screen.dart';
import 'settings/settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadData();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    await settingsProvider.loadSettings();
  }

  Future<void> _loadData() async {
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final invoiceProvider = Provider.of<InvoiceProvider>(
      context,
      listen: false,
    );
    final installmentProvider = Provider.of<InstallmentProvider>(
      context,
      listen: false,
    );
    final paymentProvider = Provider.of<PaymentProvider>(
      context,
      listen: false,
    );
    final voucherProvider = Provider.of<VoucherProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      customerProvider.loadCustomersWithDebt(),
      customerProvider.loadOverdueCustomers(),
      invoiceProvider.loadStatistics(),
      invoiceProvider.loadOverdueInvoices(),
      voucherProvider.loadStatistics(),
      voucherProvider.loadRecentVouchers(),
      installmentProvider.loadOverdueInstallments(),
      installmentProvider.loadTodaysDueInstallments(),
      paymentProvider.loadTodaysPayments(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: const [
          _HomeTab(),
          CustomersScreen(),
          InvoicesScreen(),
          VouchersScreen(),
          ReportsScreen(),
          SettingsScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'الرئيسية'),
          BottomNavigationBarItem(icon: Icon(Icons.people), label: 'العملاء'),
          BottomNavigationBarItem(
            icon: Icon(Icons.receipt_long),
            label: 'الفواتير',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.description),
            label: 'السندات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.assessment),
            label: 'التقارير',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }
}

class _HomeTab extends StatelessWidget {
  const _HomeTab();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الأقساط'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshData(context),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => _refreshData(context),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقات الإحصائيات
              _buildStatisticsSection(context),

              const SizedBox(height: 24),

              // الأقساط المتأخرة
              _buildOverdueSection(context),

              const SizedBox(height: 24),

              // الأقساط المستحقة اليوم
              _buildTodaysDueSection(context),

              const SizedBox(height: 24),

              // مدفوعات اليوم
              _buildTodaysPaymentsSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(BuildContext context) {
    return Consumer3<CustomerProvider, InvoiceProvider, PaymentProvider>(
      builder: (
        context,
        customerProvider,
        invoiceProvider,
        paymentProvider,
        child,
      ) {
        final customersWithDebt = customerProvider.customersWithDebt;
        final invoiceStats = invoiceProvider.statistics;

        int totalCustomers = customersWithDebt.length;
        double totalSales =
            (invoiceStats['total_sales'] as num?)?.toDouble() ?? 0;
        double totalProfits =
            (invoiceStats['total_profits'] as num?)?.toDouble() ?? 0;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('نظرة عامة', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DashboardCard(
                    title: 'إجمالي العملاء',
                    value: totalCustomers.toString(),
                    icon: Icons.people,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DashboardCard(
                    title: 'الفواتير النشطة',
                    value: (invoiceStats['active_invoices'] ?? 0).toString(),
                    icon: Icons.receipt_long,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DashboardCard(
                    title: 'إجمالي المبيعات',
                    value: Provider.of<SettingsProvider>(
                      context,
                    ).formatCurrency(totalSales),
                    icon: Icons.trending_up,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DashboardCard(
                    title: 'إجمالي الأرباح',
                    value: Provider.of<SettingsProvider>(
                      context,
                    ).formatCurrency(totalProfits),
                    icon: Icons.monetization_on,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildOverdueSection(BuildContext context) {
    return Consumer<InstallmentProvider>(
      builder: (context, installmentProvider, child) {
        final overdueInstallments = installmentProvider.overdueInstallments;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الأقساط المتأخرة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                if (overdueInstallments.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      // الانتقال إلى صفحة الأقساط المتأخرة
                    },
                    child: const Text('عرض الكل'),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            if (overdueInstallments.isEmpty)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green),
                      SizedBox(width: 8),
                      Text('لا توجد أقساط متأخرة'),
                    ],
                  ),
                ),
              )
            else
              ...overdueInstallments.take(3).map((installment) {
                return Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.red.shade100,
                      child: Icon(Icons.warning, color: Colors.red),
                    ),
                    title: Text(installment['customer_name'] ?? ''),
                    subtitle: Text(
                      'القسط رقم ${installment['installment_number']} - متأخر ${installment['days_overdue']} يوم',
                    ),
                    trailing: Text(
                      Provider.of<SettingsProvider>(context).formatCurrency(
                        (installment['remaining_amount'] as num?)?.toDouble() ??
                            0,
                      ),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ),
                );
              }),
          ],
        );
      },
    );
  }

  Widget _buildTodaysDueSection(BuildContext context) {
    return Consumer<InstallmentProvider>(
      builder: (context, installmentProvider, child) {
        final todaysDue = installmentProvider.todaysDueInstallments;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('مستحق اليوم', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            if (todaysDue.isEmpty)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.today, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('لا توجد أقساط مستحقة اليوم'),
                    ],
                  ),
                ),
              )
            else
              ...todaysDue.map((installment) {
                return Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.orange.shade100,
                      child: Icon(Icons.today, color: Colors.orange),
                    ),
                    title: Text(installment['customer_name'] ?? ''),
                    subtitle: Text(
                      'القسط رقم ${installment['installment_number']}',
                    ),
                    trailing: Text(
                      Provider.of<SettingsProvider>(context).formatCurrency(
                        (installment['remaining_amount'] as num?)?.toDouble() ??
                            0,
                      ),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                  ),
                );
              }),
          ],
        );
      },
    );
  }

  Widget _buildTodaysPaymentsSection(BuildContext context) {
    return Consumer<PaymentProvider>(
      builder: (context, paymentProvider, child) {
        final todaysPayments = paymentProvider.todaysPayments;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مدفوعات اليوم',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            if (todaysPayments.isEmpty)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.payment, color: Colors.grey),
                      SizedBox(width: 8),
                      Text('لا توجد مدفوعات اليوم'),
                    ],
                  ),
                ),
              )
            else
              ...todaysPayments.take(3).map((payment) {
                return Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.green.shade100,
                      child: Icon(Icons.payment, color: Colors.green),
                    ),
                    title: Text(payment['customer_name'] ?? ''),
                    subtitle: Text(
                      'القسط رقم ${payment['installment_number']}',
                    ),
                    trailing: Text(
                      Provider.of<SettingsProvider>(context).formatCurrency(
                        (payment['amount'] as num?)?.toDouble() ?? 0,
                      ),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ),
                );
              }),
          ],
        );
      },
    );
  }

  Future<void> _refreshData(BuildContext context) async {
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final loanProvider = Provider.of<LoanProvider>(context, listen: false);
    final installmentProvider = Provider.of<InstallmentProvider>(
      context,
      listen: false,
    );
    final paymentProvider = Provider.of<PaymentProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      customerProvider.refresh(),
      loanProvider.refresh(),
      installmentProvider.refresh(),
      paymentProvider.refresh(),
    ]);
  }
}
