import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/backup_provider.dart';
import '../../providers/settings_provider.dart';

class BackupScreen extends StatefulWidget {
  const BackupScreen({super.key});

  @override
  State<BackupScreen> createState() => _BackupScreenState();
}

class _BackupScreenState extends State<BackupScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<BackupProvider>(context, listen: false).checkBackupStatus();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('النسخ الاحتياطي'),
      ),
      body: Consumer<BackupProvider>(
        builder: (context, backupProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // حالة النسخ الاحتياطي
                _buildBackupStatus(backupProvider),
                
                const SizedBox(height: 24),
                
                // إعدادات Google Drive
                _buildGoogleDriveSettings(backupProvider),
                
                const SizedBox(height: 24),
                
                // النسخ الاحتياطي اليدوي
                _buildManualBackup(backupProvider),
                
                const SizedBox(height: 24),
                
                // النسخ الاحتياطي التلقائي
                _buildAutoBackup(backupProvider),
                
                const SizedBox(height: 24),
                
                // استعادة النسخة الاحتياطية
                _buildRestore(backupProvider),
                
                const SizedBox(height: 24),
                
                // إدارة النسخ الاحتياطية
                _buildBackupManagement(backupProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBackupStatus(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة النسخ الاحتياطي',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Icon(
                  backupProvider.isConnectedToGoogleDrive 
                      ? Icons.cloud_done 
                      : Icons.cloud_off,
                  color: backupProvider.isConnectedToGoogleDrive 
                      ? Colors.green 
                      : Colors.red,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        backupProvider.isConnectedToGoogleDrive 
                            ? 'متصل بـ Google Drive' 
                            : 'غير متصل بـ Google Drive',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: backupProvider.isConnectedToGoogleDrive 
                              ? Colors.green 
                              : Colors.red,
                        ),
                      ),
                      if (backupProvider.lastBackupDate != null)
                        Text(
                          'آخر نسخة احتياطية: ${_formatDateTime(backupProvider.lastBackupDate!)}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            
            if (backupProvider.isLoading) ...[
              const SizedBox(height: 16),
              const LinearProgressIndicator(),
              const SizedBox(height: 8),
              Text(
                backupProvider.loadingMessage ?? 'جاري المعالجة...',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleDriveSettings(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات Google Drive',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (!backupProvider.isConnectedToGoogleDrive) ...[
              Text(
                'قم بربط حسابك في Google Drive لحفظ النسخ الاحتياطية في السحابة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: backupProvider.isLoading 
                      ? null 
                      : () => _connectToGoogleDrive(backupProvider),
                  icon: const Icon(Icons.cloud),
                  label: const Text('ربط Google Drive'),
                ),
              ),
            ] else ...[
              Row(
                children: [
                  Icon(Icons.account_circle, color: Colors.green),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'متصل بحساب: ${backupProvider.googleAccountEmail ?? "غير معروف"}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: backupProvider.isLoading 
                      ? null 
                      : () => _disconnectFromGoogleDrive(backupProvider),
                  icon: const Icon(Icons.cloud_off),
                  label: const Text('قطع الاتصال'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildManualBackup(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'النسخ الاحتياطي اليدوي',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'قم بإنشاء نسخة احتياطية فورية من جميع البيانات',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: backupProvider.isLoading 
                        ? null 
                        : () => _createBackup(backupProvider),
                    icon: const Icon(Icons.backup),
                    label: const Text('إنشاء نسخة احتياطية'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: backupProvider.isLoading 
                        ? null 
                        : () => _exportToFile(backupProvider),
                    icon: const Icon(Icons.file_download),
                    label: const Text('تصدير إلى ملف'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoBackup(BackupProvider backupProvider) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'النسخ الاحتياطي التلقائي',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text('تفعيل النسخ الاحتياطي التلقائي'),
              subtitle: const Text('إنشاء نسخة احتياطية تلقائياً حسب الجدولة المحددة'),
              value: settingsProvider.autoBackupEnabled,
              onChanged: (value) {
                settingsProvider.setAutoBackupEnabled(value);
              },
            ),
            
            if (settingsProvider.autoBackupEnabled) ...[
              const Divider(),
              
              ListTile(
                title: const Text('تكرار النسخ الاحتياطي'),
                subtitle: Text(_getBackupFrequencyText(settingsProvider.backupFrequency)),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showBackupFrequencyDialog(settingsProvider),
              ),
              
              SwitchListTile(
                title: const Text('النسخ الاحتياطي عبر WiFi فقط'),
                subtitle: const Text('لتوفير بيانات الهاتف'),
                value: settingsProvider.backupOnWifiOnly,
                onChanged: (value) {
                  settingsProvider.setBackupOnWifiOnly(value);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRestore(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'استعادة النسخة الاحتياطية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'استعادة البيانات من نسخة احتياطية سابقة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: backupProvider.isLoading 
                        ? null 
                        : () => _restoreFromCloud(backupProvider),
                    icon: const Icon(Icons.cloud_download),
                    label: const Text('استعادة من السحابة'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: backupProvider.isLoading 
                        ? null 
                        : () => _restoreFromFile(backupProvider),
                    icon: const Icon(Icons.file_upload),
                    label: const Text('استعادة من ملف'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupManagement(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إدارة النسخ الاحتياطية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.list),
              title: const Text('عرض النسخ الاحتياطية'),
              subtitle: const Text('إدارة وحذف النسخ الاحتياطية القديمة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showBackupsList(backupProvider),
            ),
            
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('إعدادات متقدمة'),
              subtitle: const Text('تخصيص إعدادات النسخ الاحتياطي'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showAdvancedSettings(),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getBackupFrequencyText(String frequency) {
    switch (frequency) {
      case 'daily':
        return 'يومياً';
      case 'weekly':
        return 'أسبوعياً';
      case 'monthly':
        return 'شهرياً';
      default:
        return 'يومياً';
    }
  }

  Future<void> _connectToGoogleDrive(BackupProvider backupProvider) async {
    final success = await backupProvider.connectToGoogleDrive();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success 
                ? 'تم ربط Google Drive بنجاح' 
                : 'فشل في ربط Google Drive',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _disconnectFromGoogleDrive(BackupProvider backupProvider) async {
    final confirmed = await _showConfirmationDialog(
      'قطع الاتصال',
      'هل أنت متأكد من قطع الاتصال مع Google Drive؟',
    );
    
    if (confirmed) {
      await backupProvider.disconnectFromGoogleDrive();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم قطع الاتصال مع Google Drive')),
        );
      }
    }
  }

  Future<void> _createBackup(BackupProvider backupProvider) async {
    final success = await backupProvider.createBackup();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success 
                ? 'تم إنشاء النسخة الاحتياطية بنجاح' 
                : 'فشل في إنشاء النسخة الاحتياطية',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _exportToFile(BackupProvider backupProvider) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير إلى ملف - قيد التطوير')),
    );
  }

  Future<void> _restoreFromCloud(BackupProvider backupProvider) async {
    final confirmed = await _showConfirmationDialog(
      'استعادة من السحابة',
      'هل أنت متأكد من استعادة البيانات؟ سيتم استبدال البيانات الحالية.',
    );
    
    if (confirmed) {
      final success = await backupProvider.restoreFromCloud();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success 
                  ? 'تم استعادة البيانات بنجاح' 
                  : 'فشل في استعادة البيانات',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _restoreFromFile(BackupProvider backupProvider) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('استعادة من ملف - قيد التطوير')),
    );
  }

  void _showBackupFrequencyDialog(SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تكرار النسخ الاحتياطي'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<String>(
                title: const Text('يومياً'),
                value: 'daily',
                groupValue: settingsProvider.backupFrequency,
                onChanged: (value) {
                  settingsProvider.setBackupFrequency(value!);
                  Navigator.of(context).pop();
                },
              ),
              RadioListTile<String>(
                title: const Text('أسبوعياً'),
                value: 'weekly',
                groupValue: settingsProvider.backupFrequency,
                onChanged: (value) {
                  settingsProvider.setBackupFrequency(value!);
                  Navigator.of(context).pop();
                },
              ),
              RadioListTile<String>(
                title: const Text('شهرياً'),
                value: 'monthly',
                groupValue: settingsProvider.backupFrequency,
                onChanged: (value) {
                  settingsProvider.setBackupFrequency(value!);
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showBackupsList(BackupProvider backupProvider) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('قائمة النسخ الاحتياطية - قيد التطوير')),
    );
  }

  void _showAdvancedSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الإعدادات المتقدمة - قيد التطوير')),
    );
  }

  Future<bool> _showConfirmationDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('تأكيد'),
            ),
          ],
        );
      },
    );
    return result ?? false;
  }
}
