import 'package:sqflite/sqflite.dart';
import '../models/customer.dart';
import 'database_helper.dart';

class CustomerDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة عميل جديد
  Future<int> insertCustomer(Customer customer) async {
    final db = await _databaseHelper.database;
    return await db.insert('customers', customer.toMap());
  }

  // الحصول على جميع العملاء
  Future<List<Customer>> getAllCustomers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  // الحصول على عميل بواسطة ID
  Future<Customer?> getCustomerById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'id = ? AND is_active = ?',
      whereArgs: [id, 1],
    );

    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  // البحث عن العملاء
  Future<List<Customer>> searchCustomers(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: '(name LIKE ? OR phone LIKE ? OR address LIKE ?) AND is_active = ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', 1],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  // تحديث بيانات العميل
  Future<int> updateCustomer(Customer customer) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'customers',
      customer.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [customer.id],
    );
  }

  // حذف عميل (حذف منطقي)
  Future<int> deleteCustomer(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'customers',
      {
        'is_active': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // حذف عميل نهائياً
  Future<int> permanentDeleteCustomer(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على عدد العملاء
  Future<int> getCustomersCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM customers WHERE is_active = 1'
    );
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // الحصول على العملاء مع إجمالي المديونية
  Future<List<Map<String, dynamic>>> getCustomersWithDebt() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        c.*,
        COALESCE(SUM(l.total_amount), 0) as total_debt,
        COALESCE(SUM(i.paid_amount), 0) as total_paid,
        COALESCE(SUM(l.total_amount) - SUM(i.paid_amount), 0) as remaining_debt
      FROM customers c
      LEFT JOIN loans l ON c.id = l.customer_id AND l.status != 3
      LEFT JOIN installments i ON l.id = i.loan_id
      WHERE c.is_active = 1
      GROUP BY c.id
      ORDER BY c.name ASC
    ''');

    return result;
  }

  // الحصول على العملاء المتأخرين
  Future<List<Map<String, dynamic>>> getOverdueCustomers() async {
    final db = await _databaseHelper.database;
    final String today = DateTime.now().toIso8601String().split('T')[0];
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT DISTINCT
        c.*,
        COUNT(i.id) as overdue_installments,
        SUM(i.amount - i.paid_amount) as overdue_amount
      FROM customers c
      INNER JOIN loans l ON c.id = l.customer_id
      INNER JOIN installments i ON l.id = i.loan_id
      WHERE c.is_active = 1 
        AND i.status != 1 
        AND i.due_date < ?
        AND (i.amount - i.paid_amount) > 0
      GROUP BY c.id
      ORDER BY c.name ASC
    ''', [today]);

    return result;
  }

  // استعادة عميل محذوف
  Future<int> restoreCustomer(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'customers',
      {
        'is_active': 1,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على العملاء المحذوفين
  Future<List<Customer>> getDeletedCustomers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'is_active = ?',
      whereArgs: [0],
      orderBy: 'updated_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }
}
