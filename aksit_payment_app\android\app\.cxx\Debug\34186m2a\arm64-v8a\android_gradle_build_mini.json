{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\New folder\\aksit\\aksit_payment_app\\android\\app\\.cxx\\Debug\\34186m2a\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\New folder\\aksit\\aksit_payment_app\\android\\app\\.cxx\\Debug\\34186m2a\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}