import '../models/voucher.dart';
import 'database_helper.dart';

class VoucherDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة سند جديد
  Future<int> insertVoucher(Voucher voucher) async {
    final db = await _databaseHelper.database;
    return await db.insert('vouchers', voucher.toMap());
  }

  // الحصول على جميع السندات
  Future<List<Voucher>> getAllVouchers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'vouchers',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Voucher.fromMap(maps[i]);
    });
  }

  // الحصول على سندات بنوع معين
  Future<List<Voucher>> getVouchersByType(VoucherType type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'vouchers',
      where: 'type = ?',
      whereArgs: [type.index],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Voucher.fromMap(maps[i]);
    });
  }

  // الحصول على سندات عميل معين
  Future<List<Voucher>> getVouchersByCustomerId(int customerId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'vouchers',
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Voucher.fromMap(maps[i]);
    });
  }

  // الحصول على سندات فاتورة معينة
  Future<List<Voucher>> getVouchersByInvoiceId(int invoiceId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'vouchers',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Voucher.fromMap(maps[i]);
    });
  }

  // الحصول على سند بواسطة ID
  Future<Voucher?> getVoucherById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'vouchers',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Voucher.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على سند بواسطة رقم السند
  Future<Voucher?> getVoucherByNumber(String voucherNumber) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'vouchers',
      where: 'voucher_number = ?',
      whereArgs: [voucherNumber],
    );

    if (maps.isNotEmpty) {
      return Voucher.fromMap(maps.first);
    }
    return null;
  }

  // تحديث حالة السند
  Future<int> updateVoucherStatus(int voucherId, VoucherStatus status) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'vouchers',
      {
        'status': status.index,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [voucherId],
    );
  }

  // تحديث بيانات السند
  Future<int> updateVoucher(Voucher voucher) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'vouchers',
      voucher.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [voucher.id],
    );
  }

  // حذف سند
  Future<int> deleteVoucher(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'vouchers',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على السندات مع تفاصيل العميل
  Future<List<Map<String, dynamic>>> getVouchersWithCustomerDetails() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        v.*,
        c.name as customer_name,
        c.phone as customer_phone,
        inv.invoice_number
      FROM vouchers v
      LEFT JOIN customers c ON v.customer_id = c.id
      LEFT JOIN invoices inv ON v.invoice_id = inv.id
      ORDER BY v.created_at DESC
    ''');

    return result;
  }

  // الحصول على السندات لفترة معينة
  Future<List<Voucher>> getVouchersByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'vouchers',
      where: 'date(voucher_date) >= date(?) AND date(voucher_date) <= date(?)',
      whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'voucher_date DESC',
    );

    return List.generate(maps.length, (i) {
      return Voucher.fromMap(maps[i]);
    });
  }

  // الحصول على إحصائيات السندات
  Future<Map<String, dynamic>> getVouchersStatistics() async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_vouchers,
        SUM(CASE WHEN type = 0 THEN 1 ELSE 0 END) as receipt_vouchers,
        SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as payment_vouchers,
        SUM(CASE WHEN type = 0 THEN amount ELSE 0 END) as total_receipts,
        SUM(CASE WHEN type = 1 THEN amount ELSE 0 END) as total_payments,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as draft_vouchers,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as confirmed_vouchers,
        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as cancelled_vouchers
      FROM vouchers
    ''');

    return result.first;
  }

  // البحث في السندات
  Future<List<Map<String, dynamic>>> searchVouchers(String query) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        v.*,
        c.name as customer_name,
        c.phone as customer_phone,
        inv.invoice_number
      FROM vouchers v
      LEFT JOIN customers c ON v.customer_id = c.id
      LEFT JOIN invoices inv ON v.invoice_id = inv.id
      WHERE v.voucher_number LIKE ? 
        OR v.description LIKE ?
        OR c.name LIKE ?
        OR c.phone LIKE ?
        OR inv.invoice_number LIKE ?
      ORDER BY v.created_at DESC
    ''', List.filled(5, '%$query%'));

    return result;
  }

  // الحصول على تقرير السندات لفترة معينة
  Future<Map<String, dynamic>> getVouchersReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_vouchers,
        SUM(CASE WHEN type = 0 AND status = 1 THEN amount ELSE 0 END) as confirmed_receipts,
        SUM(CASE WHEN type = 1 AND status = 1 THEN amount ELSE 0 END) as confirmed_payments,
        SUM(CASE WHEN type = 0 THEN amount ELSE 0 END) as total_receipts,
        SUM(CASE WHEN type = 1 THEN amount ELSE 0 END) as total_payments,
        COUNT(CASE WHEN type = 0 THEN 1 END) as receipt_count,
        COUNT(CASE WHEN type = 1 THEN 1 END) as payment_count
      FROM vouchers
      WHERE date(voucher_date) >= date(?) 
        AND date(voucher_date) <= date(?)
    ''', [startDate.toIso8601String(), endDate.toIso8601String()]);

    return result.first;
  }

  // الحصول على آخر السندات
  Future<List<Map<String, dynamic>>> getRecentVouchers({int limit = 10}) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        v.*,
        c.name as customer_name,
        c.phone as customer_phone,
        inv.invoice_number
      FROM vouchers v
      LEFT JOIN customers c ON v.customer_id = c.id
      LEFT JOIN invoices inv ON v.invoice_id = inv.id
      ORDER BY v.created_at DESC
      LIMIT ?
    ''', [limit]);

    return result;
  }

  // تطبيق فلتر على السندات
  Future<List<Map<String, dynamic>>> getFilteredVouchers(VoucherFilter filter) async {
    final db = await _databaseHelper.database;
    
    String whereClause = '1=1';
    List<dynamic> whereArgs = [];

    if (filter.type != null) {
      whereClause += ' AND v.type = ?';
      whereArgs.add(filter.type!.index);
    }

    if (filter.status != null) {
      whereClause += ' AND v.status = ?';
      whereArgs.add(filter.status!.index);
    }

    if (filter.customerId != null) {
      whereClause += ' AND v.customer_id = ?';
      whereArgs.add(filter.customerId);
    }

    if (filter.startDate != null) {
      whereClause += ' AND date(v.voucher_date) >= date(?)';
      whereArgs.add(filter.startDate!.toIso8601String());
    }

    if (filter.endDate != null) {
      whereClause += ' AND date(v.voucher_date) <= date(?)';
      whereArgs.add(filter.endDate!.toIso8601String());
    }

    if (filter.minAmount != null) {
      whereClause += ' AND v.amount >= ?';
      whereArgs.add(filter.minAmount);
    }

    if (filter.maxAmount != null) {
      whereClause += ' AND v.amount <= ?';
      whereArgs.add(filter.maxAmount);
    }

    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        v.*,
        c.name as customer_name,
        c.phone as customer_phone,
        inv.invoice_number
      FROM vouchers v
      LEFT JOIN customers c ON v.customer_id = c.id
      LEFT JOIN invoices inv ON v.invoice_id = inv.id
      WHERE $whereClause
      ORDER BY v.created_at DESC
    ''', whereArgs);

    return result;
  }
}
