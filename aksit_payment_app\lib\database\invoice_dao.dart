import '../models/invoice.dart';
import '../models/invoice_item.dart';
import '../models/installment.dart';
import 'database_helper.dart';

class InvoiceDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة فاتورة جديدة مع العناصر والأقساط
  Future<int> insertInvoiceWithItems(
    Invoice invoice,
    List<InvoiceItem> items,
  ) async {
    final db = await _databaseHelper.database;
    
    return await db.transaction((txn) async {
      // إدراج الفاتورة
      int invoiceId = await txn.insert('invoices', invoice.toMap());
      
      // إدراج عناصر الفاتورة
      for (var item in items) {
        await txn.insert(
          'invoice_items',
          item.copyWith(invoiceId: invoiceId).toMap(),
        );
      }
      
      // إنشاء الأقساط إذا كانت الفاتورة بالتقسيط
      if (invoice.paymentMethod == PaymentMethod.installment && 
          invoice.installmentMonths != null && 
          invoice.monthlyInstallment != null) {
        
        DateTime currentDate = invoice.invoiceDate;
        for (int i = 1; i <= invoice.installmentMonths!; i++) {
          final installment = Installment(
            invoiceId: invoiceId,
            installmentNumber: i,
            amount: invoice.monthlyInstallment!,
            dueDate: currentDate,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          
          await txn.insert('installments', installment.toMap());
          
          // إضافة شهر للقسط التالي
          currentDate = DateTime(
            currentDate.year,
            currentDate.month + 1,
            currentDate.day,
          );
        }
      }
      
      return invoiceId;
    });
  }

  // الحصول على جميع الفواتير
  Future<List<Invoice>> getAllInvoices() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'invoices',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Invoice.fromMap(maps[i]);
    });
  }

  // الحصول على فواتير عميل معين
  Future<List<Invoice>> getInvoicesByCustomerId(int customerId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'invoices',
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Invoice.fromMap(maps[i]);
    });
  }

  // الحصول على فاتورة بواسطة ID
  Future<Invoice?> getInvoiceById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'invoices',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Invoice.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على فاتورة بواسطة رقم الفاتورة
  Future<Invoice?> getInvoiceByNumber(String invoiceNumber) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'invoices',
      where: 'invoice_number = ?',
      whereArgs: [invoiceNumber],
    );

    if (maps.isNotEmpty) {
      return Invoice.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على عناصر فاتورة معينة
  Future<List<InvoiceItem>> getInvoiceItems(int invoiceId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'invoice_items',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'id ASC',
    );

    return List.generate(maps.length, (i) {
      return InvoiceItem.fromMap(maps[i]);
    });
  }

  // تحديث حالة الفاتورة
  Future<int> updateInvoiceStatus(int invoiceId, InvoiceStatus status) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'invoices',
      {
        'status': status.index,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  // تحديث المبلغ المدفوع والمتبقي
  Future<int> updateInvoicePayment(
    int invoiceId,
    double paidAmount,
    double remainingAmount,
  ) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'invoices',
      {
        'paid_amount': paidAmount,
        'remaining_amount': remainingAmount,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  // تحديث بيانات الفاتورة
  Future<int> updateInvoice(Invoice invoice) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'invoices',
      invoice.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [invoice.id],
    );
  }

  // حذف فاتورة
  Future<int> deleteInvoice(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'invoices',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على إحصائيات الفواتير
  Future<Map<String, dynamic>> getInvoicesStatistics() async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_invoices,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as draft_invoices,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_invoices,
        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed_invoices,
        SUM(total_amount) as total_sales,
        SUM(cost_amount) as total_costs,
        SUM(profit_amount) as total_profits,
        SUM(paid_amount) as total_paid,
        SUM(remaining_amount) as total_remaining,
        AVG(profit_amount / total_amount * 100) as avg_profit_percentage
      FROM invoices
      WHERE status != 3
    ''');

    return result.first;
  }

  // الحصول على الفواتير مع تفاصيل العميل
  Future<List<Map<String, dynamic>>> getInvoicesWithCustomerDetails() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        i.*,
        c.name as customer_name,
        c.phone as customer_phone
      FROM invoices i
      INNER JOIN customers c ON i.customer_id = c.id
      WHERE c.is_active = 1
      ORDER BY i.created_at DESC
    ''');

    return result;
  }

  // الحصول على الفواتير المتأخرة
  Future<List<Map<String, dynamic>>> getOverdueInvoices() async {
    final db = await _databaseHelper.database;
    final String today = DateTime.now().toIso8601String().split('T')[0];
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT DISTINCT
        i.*,
        c.name as customer_name,
        c.phone as customer_phone,
        COUNT(inst.id) as overdue_installments,
        SUM(inst.amount - inst.paid_amount) as overdue_amount
      FROM invoices i
      INNER JOIN customers c ON i.customer_id = c.id
      INNER JOIN installments inst ON i.id = inst.invoice_id
      WHERE c.is_active = 1 
        AND i.status = 1
        AND inst.status != 1 
        AND inst.due_date < ?
        AND (inst.amount - inst.paid_amount) > 0
      GROUP BY i.id
      ORDER BY i.created_at DESC
    ''', [today]);

    return result;
  }

  // الحصول على أفضل العملاء (حسب قيمة المشتريات)
  Future<List<Map<String, dynamic>>> getTopCustomers({int limit = 10}) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        c.id,
        c.name,
        c.phone,
        COUNT(i.id) as total_invoices,
        SUM(i.total_amount) as total_purchases,
        SUM(i.profit_amount) as total_profit_generated
      FROM customers c
      INNER JOIN invoices i ON c.id = i.customer_id
      WHERE c.is_active = 1 AND i.status != 3
      GROUP BY c.id
      ORDER BY total_purchases DESC
      LIMIT ?
    ''', [limit]);

    return result;
  }

  // الحصول على تقرير المبيعات لفترة معينة
  Future<Map<String, dynamic>> getSalesReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_invoices,
        SUM(total_amount) as total_sales,
        SUM(cost_amount) as total_costs,
        SUM(profit_amount) as total_profits,
        AVG(profit_amount / total_amount * 100) as avg_profit_percentage
      FROM invoices
      WHERE date(invoice_date) >= date(?) 
        AND date(invoice_date) <= date(?)
        AND status != 3
    ''', [startDate.toIso8601String(), endDate.toIso8601String()]);

    return result.first;
  }
}
