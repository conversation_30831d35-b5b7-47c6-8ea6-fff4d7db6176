import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/invoice.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/settings_provider.dart';
import '../../widgets/invoice_card.dart';
import 'add_invoice_screen.dart';
import 'invoice_details_screen.dart';

class InvoicesScreen extends StatefulWidget {
  const InvoicesScreen({super.key});

  @override
  State<InvoicesScreen> createState() => _InvoicesScreenState();
}

class _InvoicesScreenState extends State<InvoicesScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<InvoiceProvider>(context, listen: false).loadInvoicesWithDetails();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفواتير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<InvoiceProvider>(context, listen: false).refresh();
            },
          ),
        ],
      ),
      body: Consumer<InvoiceProvider>(
        builder: (context, invoiceProvider, child) {
          if (invoiceProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (invoiceProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    invoiceProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      invoiceProvider.clearError();
                      invoiceProvider.refresh();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final invoicesWithDetails = invoiceProvider.invoicesWithDetails;

          if (invoicesWithDetails.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد فواتير',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ابدأ بإنشاء فاتورة جديدة',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _addInvoice,
                    icon: const Icon(Icons.add),
                    label: const Text('إنشاء فاتورة'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => invoiceProvider.refresh(),
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: invoicesWithDetails.length,
              itemBuilder: (context, index) {
                final invoiceData = invoicesWithDetails[index];
                final invoice = Invoice.fromMap(invoiceData);
                
                return InvoiceCard(
                  invoice: invoice,
                  customerName: invoiceData['customer_name'] ?? '',
                  customerPhone: invoiceData['customer_phone'] ?? '',
                  onTap: () => _viewInvoiceDetails(invoice),
                  onEdit: () => _editInvoice(invoice),
                  onDelete: () => _deleteInvoice(invoice),
                );
              },
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addInvoice,
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('البحث في الفواتير'),
          content: const TextField(
            decoration: InputDecoration(
              hintText: 'ادخل رقم الفاتورة أو اسم العميل',
              prefixIcon: Icon(Icons.search),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // تطبيق البحث
              },
              child: const Text('بحث'),
            ),
          ],
        );
      },
    );
  }

  void _addInvoice() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddInvoiceScreen(),
      ),
    );
  }

  void _editInvoice(Invoice invoice) {
    // سيتم تطبيقها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل الفاتورة - قيد التطوير')),
    );
  }

  void _viewInvoiceDetails(Invoice invoice) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => InvoiceDetailsScreen(invoice: invoice),
      ),
    );
  }

  void _deleteInvoice(Invoice invoice) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف الفاتورة "${invoice.invoiceNumber}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                final invoiceProvider = Provider.of<InvoiceProvider>(
                  context,
                  listen: false,
                );
                
                navigator.pop();
                final success = await invoiceProvider.deleteInvoice(invoice.id!);

                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        success ? 'تم حذف الفاتورة بنجاح' : 'فشل في حذف الفاتورة',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
