import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/voucher.dart';
import '../../providers/voucher_provider.dart';
import '../../widgets/voucher_card.dart';
import 'add_voucher_screen.dart';
import 'voucher_details_screen.dart';

class VouchersScreen extends StatefulWidget {
  const VouchersScreen({super.key});

  @override
  State<VouchersScreen> createState() => _VouchersScreenState();
}

class _VouchersScreenState extends State<VouchersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  VoucherType? _selectedType;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<VoucherProvider>(context, listen: false).loadVouchersWithDetails();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('السندات المالية'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.list)),
            Tab(text: 'سندات القبض', icon: Icon(Icons.receipt)),
            Tab(text: 'سندات الدفع', icon: Icon(Icons.payment)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<VoucherProvider>(context, listen: false).refresh();
            },
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildVouchersList(null),
          _buildVouchersList(VoucherType.receipt),
          _buildVouchersList(VoucherType.payment),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addVoucher,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildVouchersList(VoucherType? type) {
    return Consumer<VoucherProvider>(
      builder: (context, voucherProvider, child) {
        if (voucherProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (voucherProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red.shade300,
                ),
                const SizedBox(height: 16),
                Text(
                  voucherProvider.error!,
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    voucherProvider.clearError();
                    voucherProvider.refresh();
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        final vouchersWithDetails = voucherProvider.vouchersWithDetails;
        final filteredVouchers = type == null
            ? vouchersWithDetails
            : vouchersWithDetails.where((v) => 
                VoucherType.values[v['type']] == type).toList();

        if (filteredVouchers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  type == VoucherType.receipt 
                      ? Icons.receipt_outlined
                      : type == VoucherType.payment
                          ? Icons.payment_outlined
                          : Icons.description_outlined,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  type == null 
                      ? 'لا توجد سندات'
                      : type == VoucherType.receipt
                          ? 'لا توجد سندات قبض'
                          : 'لا توجد سندات دفع',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'ابدأ بإنشاء سند جديد',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _addVoucher,
                  icon: const Icon(Icons.add),
                  label: const Text('إنشاء سند'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => voucherProvider.refresh(),
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: filteredVouchers.length,
            itemBuilder: (context, index) {
              final voucherData = filteredVouchers[index];
              final voucher = Voucher.fromMap(voucherData);
              
              return VoucherCard(
                voucher: voucher,
                customerName: voucherData['customer_name'] ?? '',
                invoiceNumber: voucherData['invoice_number'] ?? '',
                onTap: () => _viewVoucherDetails(voucher),
                onEdit: () => _editVoucher(voucher),
                onDelete: () => _deleteVoucher(voucher),
                onStatusChange: (status) => _changeVoucherStatus(voucher, status),
              );
            },
          ),
        );
      },
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('البحث في السندات'),
          content: const TextField(
            decoration: InputDecoration(
              hintText: 'ادخل رقم السند أو اسم العميل',
              prefixIcon: Icon(Icons.search),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // تطبيق البحث
              },
              child: const Text('بحث'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تصفية السندات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<VoucherType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع السند',
                  prefixIcon: Icon(Icons.category),
                ),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('جميع الأنواع'),
                  ),
                  ...VoucherType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type == VoucherType.receipt ? 'سند قبض' : 'سند دفع'),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedType = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // تطبيق الفلتر
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  void _addVoucher() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddVoucherScreen(),
      ),
    );
  }

  void _editVoucher(Voucher voucher) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddVoucherScreen(voucher: voucher),
      ),
    );
  }

  void _viewVoucherDetails(Voucher voucher) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VoucherDetailsScreen(voucher: voucher),
      ),
    );
  }

  void _changeVoucherStatus(Voucher voucher, VoucherStatus status) async {
    final voucherProvider = Provider.of<VoucherProvider>(context, listen: false);
    final success = await voucherProvider.updateVoucherStatus(voucher.id!, status);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success 
                ? 'تم تحديث حالة السند بنجاح' 
                : 'فشل في تحديث حالة السند',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _deleteVoucher(Voucher voucher) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف السند "${voucher.voucherNumber}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                final voucherProvider = Provider.of<VoucherProvider>(
                  context,
                  listen: false,
                );
                
                navigator.pop();
                final success = await voucherProvider.deleteVoucher(voucher.id!);

                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        success ? 'تم حذف السند بنجاح' : 'فشل في حذف السند',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
