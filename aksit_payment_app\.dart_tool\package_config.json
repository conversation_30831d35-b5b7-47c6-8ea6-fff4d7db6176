{"configVersion": 2, "packages": [{"name": "_discoveryapis_commons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_discoveryapis_commons-1.0.7", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "archive", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "barcode", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/barcode-2.2.9", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "bidi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bidi-2.0.13", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cached_network_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-8.3.7", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "file_selector_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "file_selector_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///C:/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_cache_manager", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_localizations", "rootUri": "file:///C:/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_test", "rootUri": "file:///C:/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///C:/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "google_identity_services_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3+1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-6.3.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-6.2.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-5.9.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-0.12.4+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "googlea<PERSON>", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/googleapis-13.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "googleapis_auth", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/googleapis_auth-1.6.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "image_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+23", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "image_picker_for_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "intl", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_parsing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pdf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pdf-3.11.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "pdf_widget_wrapper", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pdf_widget_wrapper-1.0.4", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "posix", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "printing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/printing-5.14.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "qr", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr-3.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "rxdart", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "share_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-10.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "share_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-5.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///C:/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_darwin", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "synchronized", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.3.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "win32", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.13.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "xdg_directories", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "aksit_payment_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.7"}], "generated": "2025-07-02T20:48:27.866485Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///C:/flutter", "flutterVersion": "3.29.3", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}