import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'aksit_payments.db');

    return await openDatabase(
      path,
      version: 2,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // إنشاء جدول العملاء
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        address TEXT NOT NULL,
        notes TEXT,
        image_path TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1
      )
    ''');

    // إنشاء جدول الفواتير
    await db.execute('''
      CREATE TABLE invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        customer_id INTEGER NOT NULL,
        invoice_date TEXT NOT NULL,
        total_amount REAL NOT NULL,
        cost_amount REAL NOT NULL,
        profit_amount REAL NOT NULL,
        paid_amount REAL NOT NULL DEFAULT 0,
        remaining_amount REAL NOT NULL,
        payment_method INTEGER NOT NULL DEFAULT 0,
        installment_months INTEGER,
        monthly_installment REAL,
        status INTEGER NOT NULL DEFAULT 0,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء جدول عناصر الفواتير
    await db.execute('''
      CREATE TABLE invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        product_name TEXT NOT NULL,
        product_description TEXT,
        quantity REAL NOT NULL,
        unit TEXT NOT NULL DEFAULT 'قطعة',
        unit_cost REAL NOT NULL,
        unit_price REAL NOT NULL,
        total_cost REAL NOT NULL,
        total_price REAL NOT NULL,
        profit REAL NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء جدول القروض
    await db.execute('''
      CREATE TABLE loans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        total_amount REAL NOT NULL,
        number_of_installments INTEGER NOT NULL,
        installment_amount REAL NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT,
        status INTEGER NOT NULL DEFAULT 0,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء جدول الأقساط
    await db.execute('''
      CREATE TABLE installments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        installment_number INTEGER NOT NULL,
        amount REAL NOT NULL,
        due_date TEXT NOT NULL,
        paid_date TEXT,
        paid_amount REAL NOT NULL DEFAULT 0,
        status INTEGER NOT NULL DEFAULT 0,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء جدول المدفوعات
    await db.execute('''
      CREATE TABLE payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        installment_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        payment_date TEXT NOT NULL,
        type INTEGER NOT NULL DEFAULT 0,
        notes TEXT,
        receipt_number TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (installment_id) REFERENCES installments (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء فهارس لتحسين الأداء
    await db.execute('CREATE INDEX idx_customers_phone ON customers (phone)');
    await db.execute(
      'CREATE INDEX idx_invoices_customer_id ON invoices (customer_id)',
    );
    await db.execute(
      'CREATE INDEX idx_invoices_number ON invoices (invoice_number)',
    );
    await db.execute(
      'CREATE INDEX idx_invoices_date ON invoices (invoice_date)',
    );
    await db.execute(
      'CREATE INDEX idx_invoice_items_invoice_id ON invoice_items (invoice_id)',
    );
    await db.execute(
      'CREATE INDEX idx_loans_customer_id ON loans (customer_id)',
    );
    await db.execute(
      'CREATE INDEX idx_installments_invoice_id ON installments (invoice_id)',
    );
    await db.execute(
      'CREATE INDEX idx_installments_due_date ON installments (due_date)',
    );
    await db.execute(
      'CREATE INDEX idx_payments_installment_id ON payments (installment_id)',
    );
    await db.execute(
      'CREATE INDEX idx_payments_payment_date ON payments (payment_date)',
    );
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // إضافة جداول الفواتير في الإصدار 2
      await db.execute('''
        CREATE TABLE invoices (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_number TEXT NOT NULL UNIQUE,
          customer_id INTEGER NOT NULL,
          invoice_date TEXT NOT NULL,
          total_amount REAL NOT NULL,
          cost_amount REAL NOT NULL,
          profit_amount REAL NOT NULL,
          paid_amount REAL NOT NULL DEFAULT 0,
          remaining_amount REAL NOT NULL,
          payment_method INTEGER NOT NULL DEFAULT 0,
          installment_months INTEGER,
          monthly_installment REAL,
          status INTEGER NOT NULL DEFAULT 0,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      await db.execute('''
        CREATE TABLE invoice_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_id INTEGER NOT NULL,
          product_name TEXT NOT NULL,
          product_description TEXT,
          quantity REAL NOT NULL,
          unit TEXT NOT NULL DEFAULT 'قطعة',
          unit_cost REAL NOT NULL,
          unit_price REAL NOT NULL,
          total_cost REAL NOT NULL,
          total_price REAL NOT NULL,
          profit REAL NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
        )
      ''');

      // تحديث جدول الأقساط لربطه بالفواتير بدلاً من القروض
      await db.execute('ALTER TABLE installments RENAME TO installments_old');

      await db.execute('''
        CREATE TABLE installments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_id INTEGER NOT NULL,
          installment_number INTEGER NOT NULL,
          amount REAL NOT NULL,
          due_date TEXT NOT NULL,
          paid_date TEXT,
          paid_amount REAL NOT NULL DEFAULT 0,
          status INTEGER NOT NULL DEFAULT 0,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
        )
      ''');

      // نسخ البيانات القديمة إذا كانت موجودة
      await db.execute('''
        INSERT INTO installments (id, invoice_id, installment_number, amount, due_date, paid_date, paid_amount, status, notes, created_at, updated_at)
        SELECT id, loan_id, installment_number, amount, due_date, paid_date, paid_amount, status, notes, created_at, updated_at
        FROM installments_old
      ''');

      await db.execute('DROP TABLE installments_old');

      // إضافة الفهارس الجديدة
      await db.execute(
        'CREATE INDEX idx_invoices_customer_id ON invoices (customer_id)',
      );
      await db.execute(
        'CREATE INDEX idx_invoices_number ON invoices (invoice_number)',
      );
      await db.execute(
        'CREATE INDEX idx_invoices_date ON invoices (invoice_date)',
      );
      await db.execute(
        'CREATE INDEX idx_invoice_items_invoice_id ON invoice_items (invoice_id)',
      );
      await db.execute(
        'CREATE INDEX idx_installments_invoice_id ON installments (invoice_id)',
      );
    }
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  // حذف قاعدة البيانات
  Future<void> deleteDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'aksit_payments.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }

  // الحصول على مسار قاعدة البيانات
  Future<String> getDatabasePath() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    return join(documentsDirectory.path, 'aksit_payments.db');
  }

  // نسخ احتياطي من قاعدة البيانات
  Future<File> backupDatabase() async {
    final db = await database;
    await db.close();

    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String dbPath = join(documentsDirectory.path, 'aksit_payments.db');
    String backupPath = join(
      documentsDirectory.path,
      'aksit_payments_backup_${DateTime.now().millisecondsSinceEpoch}.db',
    );

    File dbFile = File(dbPath);
    File backupFile = await dbFile.copy(backupPath);

    // إعادة فتح قاعدة البيانات
    _database = await _initDatabase();

    return backupFile;
  }

  // استعادة قاعدة البيانات من نسخة احتياطية
  Future<void> restoreDatabase(String backupPath) async {
    await close();

    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String dbPath = join(documentsDirectory.path, 'aksit_payments.db');

    File backupFile = File(backupPath);
    if (await backupFile.exists()) {
      await backupFile.copy(dbPath);
      _database = await _initDatabase();
    } else {
      throw Exception('ملف النسخة الاحتياطية غير موجود');
    }
  }
}
