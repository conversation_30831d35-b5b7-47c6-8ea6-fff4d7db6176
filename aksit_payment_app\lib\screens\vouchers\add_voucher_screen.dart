import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/customer.dart';
import '../../models/invoice.dart';
import '../../models/voucher.dart';
import '../../providers/customer_provider.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/voucher_provider.dart';
import '../../providers/settings_provider.dart';

class AddVoucherScreen extends StatefulWidget {
  final Voucher? voucher;

  const AddVoucherScreen({super.key, this.voucher});

  @override
  State<AddVoucherScreen> createState() => _AddVoucherScreenState();
}

class _AddVoucherScreenState extends State<AddVoucherScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  VoucherType _type = VoucherType.receipt;
  Customer? _selectedCustomer;
  Invoice? _selectedInvoice;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  bool get _isEditing => widget.voucher != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _initializeForEditing();
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false).loadCustomers();
      Provider.of<InvoiceProvider>(context, listen: false).loadInvoices();
    });
  }

  void _initializeForEditing() {
    final voucher = widget.voucher!;
    _type = voucher.type;
    _amountController.text = voucher.amount.toString();
    _descriptionController.text = voucher.description;
    _notesController.text = voucher.notes ?? '';
    _selectedDate = voucher.voucherDate;
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل السند' : 'إنشاء سند جديد'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveVoucher,
            child:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Text('حفظ', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // نوع السند
              _buildVoucherTypeSelection(),

              const SizedBox(height: 24),

              // المبلغ
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ *',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  if (double.tryParse(value) == null ||
                      double.parse(value) <= 0) {
                    return 'يرجى إدخال مبلغ صحيح';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // الوصف
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف *',
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال وصف السند';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // التاريخ
              InkWell(
                onTap: _selectDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ السند',
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // اختيار العميل
              _buildCustomerSelection(),

              const SizedBox(height: 16),

              // اختيار الفاتورة
              _buildInvoiceSelection(),

              const SizedBox(height: 16),

              // الملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
              ),

              const SizedBox(height: 32),

              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed:
                          _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveVoucher,
                      child:
                          _isLoading
                              ? const CircularProgressIndicator(
                                color: Colors.white,
                              )
                              : Text(_isEditing ? 'تحديث السند' : 'حفظ السند'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVoucherTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('نوع السند', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<VoucherType>(
                title: const Text('سند قبض'),
                subtitle: const Text('استلام مبلغ'),
                value: VoucherType.receipt,
                groupValue: _type,
                onChanged:
                    _isEditing
                        ? null
                        : (value) {
                          setState(() {
                            _type = value!;
                          });
                        },
              ),
            ),
            Expanded(
              child: RadioListTile<VoucherType>(
                title: const Text('سند دفع'),
                subtitle: const Text('دفع مبلغ'),
                value: VoucherType.payment,
                groupValue: _type,
                onChanged:
                    _isEditing
                        ? null
                        : (value) {
                          setState(() {
                            _type = value!;
                          });
                        },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomerSelection() {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العميل (اختياري)',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<Customer>(
              value: _selectedCustomer,
              decoration: const InputDecoration(
                prefixIcon: Icon(Icons.person),
                hintText: 'اختر العميل',
              ),
              items: [
                const DropdownMenuItem<Customer>(
                  value: null,
                  child: Text('بدون عميل'),
                ),
                ...customerProvider.customers.map((customer) {
                  return DropdownMenuItem<Customer>(
                    value: customer,
                    child: Text('${customer.name} - ${customer.phone}'),
                  );
                }),
              ],
              onChanged: (customer) {
                setState(() {
                  _selectedCustomer = customer;
                  _selectedInvoice = null; // إعادة تعيين الفاتورة
                });
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildInvoiceSelection() {
    return Consumer<InvoiceProvider>(
      builder: (context, invoiceProvider, child) {
        final customerInvoices =
            _selectedCustomer != null
                ? invoiceProvider.invoicesWithDetails
                    .where((inv) => inv['customer_id'] == _selectedCustomer!.id)
                    .toList()
                : <Map<String, dynamic>>[];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الفاتورة (اختياري)',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<Invoice>(
              value: _selectedInvoice,
              decoration: const InputDecoration(
                prefixIcon: Icon(Icons.receipt_long),
                hintText: 'اختر الفاتورة',
              ),
              items: [
                const DropdownMenuItem<Invoice>(
                  value: null,
                  child: Text('بدون فاتورة'),
                ),
                ...customerInvoices.map((invoiceData) {
                  final invoice = Invoice.fromMap(invoiceData);
                  return DropdownMenuItem<Invoice>(
                    value: invoice,
                    child: Text(
                      '${invoice.invoiceNumber} - ${Provider.of<SettingsProvider>(context).formatCurrency(invoice.totalAmount)}',
                    ),
                  );
                }),
              ],
              onChanged:
                  _selectedCustomer != null
                      ? (invoice) {
                        setState(() {
                          _selectedInvoice = invoice;
                        });
                      }
                      : null,
            ),
            if (_selectedCustomer == null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'يجب اختيار عميل أولاً لعرض الفواتير',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
                ),
              ),
          ],
        );
      },
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveVoucher() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final voucherProvider = Provider.of<VoucherProvider>(
        context,
        listen: false,
      );

      final voucher = Voucher(
        id: _isEditing ? widget.voucher!.id : null,
        voucherNumber:
            _isEditing
                ? widget.voucher!.voucherNumber
                : voucherProvider.generateVoucherNumber(_type),
        type: _type,
        customerId: _selectedCustomer?.id,
        invoiceId: _selectedInvoice?.id,
        amount: double.parse(_amountController.text),
        description: _descriptionController.text.trim(),
        voucherDate: _selectedDate,
        status: _isEditing ? widget.voucher!.status : VoucherStatus.draft,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        createdAt: _isEditing ? widget.voucher!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success =
          _isEditing
              ? await voucherProvider.updateVoucher(voucher)
              : await voucherProvider.addVoucher(voucher);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isEditing ? 'تم تحديث السند بنجاح' : 'تم إنشاء السند بنجاح',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isEditing ? 'فشل في تحديث السند' : 'فشل في إنشاء السند',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
