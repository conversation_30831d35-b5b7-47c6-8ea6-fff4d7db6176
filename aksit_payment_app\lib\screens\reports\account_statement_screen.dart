import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/customer.dart';
import '../../models/invoice.dart';
import '../../models/voucher.dart';
import '../../providers/customer_provider.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/voucher_provider.dart';
import '../../providers/settings_provider.dart';

class AccountStatementScreen extends StatefulWidget {
  final Customer? customer;

  const AccountStatementScreen({super.key, this.customer});

  @override
  State<AccountStatementScreen> createState() => _AccountStatementScreenState();
}

class _AccountStatementScreenState extends State<AccountStatementScreen> {
  Customer? _selectedCustomer;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  List<Map<String, dynamic>> _transactions = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedCustomer = widget.customer;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false).loadCustomers();
      if (_selectedCustomer != null) {
        _loadAccountStatement();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('كشف الحساب'),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _selectedCustomer != null ? _printStatement : null,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _selectedCustomer != null ? _shareStatement : null,
          ),
        ],
      ),
      body: Column(
        children: [
          // فلاتر البحث
          _buildFilters(),
          
          // كشف الحساب
          Expanded(
            child: _selectedCustomer == null
                ? _buildSelectCustomerMessage()
                : _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildAccountStatement(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // اختيار العميل
            Consumer<CustomerProvider>(
              builder: (context, customerProvider, child) {
                return DropdownButtonFormField<Customer>(
                  value: _selectedCustomer,
                  decoration: const InputDecoration(
                    labelText: 'اختر العميل',
                    prefixIcon: Icon(Icons.person),
                  ),
                  items: customerProvider.customers.map((customer) {
                    return DropdownMenuItem<Customer>(
                      value: customer,
                      child: Text('${customer.name} - ${customer.phone}'),
                    );
                  }).toList(),
                  onChanged: (customer) {
                    setState(() {
                      _selectedCustomer = customer;
                      _transactions.clear();
                    });
                    if (customer != null) {
                      _loadAccountStatement();
                    }
                  },
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // اختيار التاريخ
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _selectStartDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'من تاريخ',
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(_formatDate(_startDate)),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectEndDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'إلى تاريخ',
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(_formatDate(_endDate)),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // زر البحث
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _selectedCustomer != null ? _loadAccountStatement : null,
                icon: const Icon(Icons.search),
                label: const Text('عرض كشف الحساب'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectCustomerMessage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_search,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'اختر عميل لعرض كشف الحساب',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountStatement() {
    if (_transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد معاملات في هذه الفترة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // معلومات العميل
        _buildCustomerInfo(),
        
        // ملخص الحساب
        _buildAccountSummary(),
        
        // قائمة المعاملات
        Expanded(
          child: _buildTransactionsList(),
        ),
      ],
    );
  }

  Widget _buildCustomerInfo() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.blue.shade100,
              child: Text(
                _selectedCustomer!.name.substring(0, 1),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _selectedCustomer!.name,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _selectedCustomer!.phone,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    _selectedCustomer!.address,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSummary() {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    double totalDebit = 0;
    double totalCredit = 0;

    for (var transaction in _transactions) {
      if (transaction['type'] == 'debit') {
        totalDebit += transaction['amount'] as double;
      } else {
        totalCredit += transaction['amount'] as double;
      }
    }

    final balance = totalDebit - totalCredit;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الحساب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المديونية',
                    settingsProvider.formatCurrency(totalDebit),
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المدفوع',
                    settingsProvider.formatCurrency(totalCredit),
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'الرصيد',
                    settingsProvider.formatCurrency(balance.abs()),
                    balance >= 0 ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String amount, Color color) {
    return Column(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          amount,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTransactionsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction = _transactions[index];
        return _buildTransactionItem(transaction);
      },
    );
  }

  Widget _buildTransactionItem(Map<String, dynamic> transaction) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isDebit = transaction['type'] == 'debit';
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isDebit ? Colors.red.shade100 : Colors.green.shade100,
          child: Icon(
            isDebit ? Icons.arrow_upward : Icons.arrow_downward,
            color: isDebit ? Colors.red : Colors.green,
          ),
        ),
        title: Text(transaction['description']),
        subtitle: Text(_formatDate(DateTime.parse(transaction['date']))),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              settingsProvider.formatCurrency(transaction['amount']),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isDebit ? Colors.red : Colors.green,
              ),
            ),
            Text(
              isDebit ? 'مدين' : 'دائن',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadAccountStatement() async {
    if (_selectedCustomer == null) return;

    setState(() => _isLoading = true);

    try {
      final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
      final voucherProvider = Provider.of<VoucherProvider>(context, listen: false);

      // تحميل الفواتير
      final invoices = await invoiceProvider.getInvoicesByCustomerId(_selectedCustomer!.id!);
      
      // تحميل السندات
      final vouchers = await voucherProvider.getVouchersByCustomerId(_selectedCustomer!.id!);

      List<Map<String, dynamic>> transactions = [];

      // إضافة الفواتير كمعاملات مدينة
      for (var invoice in invoices) {
        if (invoice.invoiceDate.isAfter(_startDate) && invoice.invoiceDate.isBefore(_endDate.add(const Duration(days: 1)))) {
          transactions.add({
            'type': 'debit',
            'description': 'فاتورة ${invoice.invoiceNumber}',
            'amount': invoice.totalAmount,
            'date': invoice.invoiceDate.toIso8601String(),
          });
        }
      }

      // إضافة سندات القبض كمعاملات دائنة
      for (var voucher in vouchers) {
        if (voucher.voucherDate.isAfter(_startDate) && voucher.voucherDate.isBefore(_endDate.add(const Duration(days: 1)))) {
          if (voucher.type == VoucherType.receipt) {
            transactions.add({
              'type': 'credit',
              'description': 'سند قبض ${voucher.voucherNumber}',
              'amount': voucher.amount,
              'date': voucher.voucherDate.toIso8601String(),
            });
          }
        }
      }

      // ترتيب المعاملات حسب التاريخ
      transactions.sort((a, b) => DateTime.parse(a['date']).compareTo(DateTime.parse(b['date'])));

      setState(() {
        _transactions = transactions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل كشف الحساب: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _printStatement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة كشف الحساب - قيد التطوير')),
    );
  }

  void _shareStatement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة كشف الحساب - قيد التطوير')),
    );
  }
}
