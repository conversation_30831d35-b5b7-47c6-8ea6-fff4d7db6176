import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:path_provider/path_provider.dart';
import '../database/database_helper.dart';

class BackupProvider extends ChangeNotifier {
  bool _isLoading = false;
  String? _loadingMessage;
  bool _isConnectedToGoogleDrive = false;
  String? _googleAccountEmail;
  DateTime? _lastBackupDate;
  
  // Google Drive
  GoogleSignIn? _googleSignIn;
  drive.DriveApi? _driveApi;

  // Getters
  bool get isLoading => _isLoading;
  String? get loadingMessage => _loadingMessage;
  bool get isConnectedToGoogleDrive => _isConnectedToGoogleDrive;
  String? get googleAccountEmail => _googleAccountEmail;
  DateTime? get lastBackupDate => _lastBackupDate;

  BackupProvider() {
    _initializeGoogleSignIn();
  }

  void _initializeGoogleSignIn() {
    _googleSignIn = GoogleSignIn(
      scopes: [
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/drive.appdata',
      ],
    );
  }

  void _setLoading(bool loading, [String? message]) {
    _isLoading = loading;
    _loadingMessage = message;
    notifyListeners();
  }

  // فحص حالة النسخ الاحتياطي
  Future<void> checkBackupStatus() async {
    _setLoading(true, 'فحص حالة النسخ الاحتياطي...');

    try {
      // فحص اتصال Google Drive
      final account = await _googleSignIn?.signInSilently();
      if (account != null) {
        _isConnectedToGoogleDrive = true;
        _googleAccountEmail = account.email;
        await _initializeDriveApi(account);
      }

      // تحميل تاريخ آخر نسخة احتياطية
      await _loadLastBackupDate();
    } catch (e) {
      debugPrint('خطأ في فحص حالة النسخ الاحتياطي: $e');
    } finally {
      _setLoading(false);
    }
  }

  // ربط Google Drive
  Future<bool> connectToGoogleDrive() async {
    _setLoading(true, 'ربط Google Drive...');

    try {
      final account = await _googleSignIn?.signIn();
      if (account != null) {
        _isConnectedToGoogleDrive = true;
        _googleAccountEmail = account.email;
        await _initializeDriveApi(account);
        _setLoading(false);
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في ربط Google Drive: $e');
    }

    _setLoading(false);
    return false;
  }

  // قطع اتصال Google Drive
  Future<void> disconnectFromGoogleDrive() async {
    _setLoading(true, 'قطع الاتصال...');

    try {
      await _googleSignIn?.signOut();
      _isConnectedToGoogleDrive = false;
      _googleAccountEmail = null;
      _driveApi = null;
    } catch (e) {
      debugPrint('خطأ في قطع الاتصال: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تهيئة Drive API
  Future<void> _initializeDriveApi(GoogleSignInAccount account) async {
    final authHeaders = await account.authHeaders;
    final authenticateClient = GoogleAuthClient(authHeaders);
    _driveApi = drive.DriveApi(authenticateClient);
  }

  // إنشاء نسخة احتياطية
  Future<bool> createBackup() async {
    _setLoading(true, 'إنشاء نسخة احتياطية...');

    try {
      // تصدير البيانات من قاعدة البيانات
      final backupData = await _exportDatabaseData();
      
      // حفظ النسخة محلياً
      final localFile = await _saveBackupLocally(backupData);
      
      // رفع إلى Google Drive إذا كان متصلاً
      if (_isConnectedToGoogleDrive && _driveApi != null) {
        await _uploadToGoogleDrive(localFile);
      }

      // تحديث تاريخ آخر نسخة احتياطية
      _lastBackupDate = DateTime.now();
      await _saveLastBackupDate();

      _setLoading(false);
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء النسخة الاحتياطية: $e');
      _setLoading(false);
      return false;
    }
  }

  // استعادة من السحابة
  Future<bool> restoreFromCloud() async {
    if (!_isConnectedToGoogleDrive || _driveApi == null) {
      return false;
    }

    _setLoading(true, 'استعادة البيانات من السحابة...');

    try {
      // البحث عن آخر نسخة احتياطية
      final backupFile = await _findLatestBackupFile();
      if (backupFile == null) {
        _setLoading(false);
        return false;
      }

      // تحميل الملف
      final backupData = await _downloadFromGoogleDrive(backupFile.id!);
      
      // استعادة البيانات
      await _restoreDatabaseData(backupData);

      _setLoading(false);
      return true;
    } catch (e) {
      debugPrint('خطأ في استعادة البيانات: $e');
      _setLoading(false);
      return false;
    }
  }

  // تصدير بيانات قاعدة البيانات
  Future<Map<String, dynamic>> _exportDatabaseData() async {
    final db = await DatabaseHelper.instance.database;
    
    final customers = await db.query('customers');
    final invoices = await db.query('invoices');
    final invoiceItems = await db.query('invoice_items');
    final installments = await db.query('installments');
    final payments = await db.query('payments');
    final vouchers = await db.query('vouchers');
    final products = await db.query('products');

    return {
      'version': '1.0',
      'timestamp': DateTime.now().toIso8601String(),
      'data': {
        'customers': customers,
        'invoices': invoices,
        'invoice_items': invoiceItems,
        'installments': installments,
        'payments': payments,
        'vouchers': vouchers,
        'products': products,
      },
    };
  }

  // حفظ النسخة الاحتياطية محلياً
  Future<File> _saveBackupLocally(Map<String, dynamic> backupData) async {
    final directory = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${directory.path}/backups');
    
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final file = File('${backupDir.path}/backup_$timestamp.json');
    
    await file.writeAsString(jsonEncode(backupData));
    return file;
  }

  // رفع إلى Google Drive
  Future<void> _uploadToGoogleDrive(File file) async {
    if (_driveApi == null) return;

    final fileName = 'aksit_backup_${DateTime.now().millisecondsSinceEpoch}.json';
    
    final driveFile = drive.File()
      ..name = fileName
      ..parents = ['appDataFolder'];

    final media = drive.Media(file.openRead(), file.lengthSync());
    
    await _driveApi!.files.create(
      driveFile,
      uploadMedia: media,
    );
  }

  // البحث عن آخر نسخة احتياطية
  Future<drive.File?> _findLatestBackupFile() async {
    if (_driveApi == null) return null;

    final fileList = await _driveApi!.files.list(
      q: "parents in 'appDataFolder' and name contains 'aksit_backup'",
      orderBy: 'createdTime desc',
      pageSize: 1,
    );

    if (fileList.files?.isNotEmpty == true) {
      return fileList.files!.first;
    }

    return null;
  }

  // تحميل من Google Drive
  Future<Map<String, dynamic>> _downloadFromGoogleDrive(String fileId) async {
    if (_driveApi == null) throw Exception('Drive API غير متاح');

    final media = await _driveApi!.files.get(
      fileId,
      downloadOptions: drive.DownloadOptions.fullMedia,
    ) as drive.Media;

    final bytes = <int>[];
    await for (final chunk in media.stream) {
      bytes.addAll(chunk);
    }

    final jsonString = utf8.decode(bytes);
    return jsonDecode(jsonString);
  }

  // استعادة بيانات قاعدة البيانات
  Future<void> _restoreDatabaseData(Map<String, dynamic> backupData) async {
    final db = await DatabaseHelper.instance.database;
    
    // بدء معاملة
    await db.transaction((txn) async {
      // حذف البيانات الحالية
      await txn.delete('customers');
      await txn.delete('invoices');
      await txn.delete('invoice_items');
      await txn.delete('installments');
      await txn.delete('payments');
      await txn.delete('vouchers');
      await txn.delete('products');

      // استعادة البيانات
      final data = backupData['data'] as Map<String, dynamic>;
      
      for (final customer in data['customers'] as List) {
        await txn.insert('customers', customer);
      }
      
      for (final invoice in data['invoices'] as List) {
        await txn.insert('invoices', invoice);
      }
      
      for (final item in data['invoice_items'] as List) {
        await txn.insert('invoice_items', item);
      }
      
      for (final installment in data['installments'] as List) {
        await txn.insert('installments', installment);
      }
      
      for (final payment in data['payments'] as List) {
        await txn.insert('payments', payment);
      }
      
      for (final voucher in data['vouchers'] as List) {
        await txn.insert('vouchers', voucher);
      }
      
      for (final product in data['products'] as List) {
        await txn.insert('products', product);
      }
    });
  }

  // تحميل تاريخ آخر نسخة احتياطية
  Future<void> _loadLastBackupDate() async {
    // يمكن حفظ هذا في SharedPreferences
    // في الوقت الحالي سنتركه فارغاً
  }

  // حفظ تاريخ آخر نسخة احتياطية
  Future<void> _saveLastBackupDate() async {
    // يمكن حفظ هذا في SharedPreferences
    // في الوقت الحالي سنتركه فارغاً
  }
}

// Google Auth Client
class GoogleAuthClient extends BaseClient {
  final Map<String, String> _headers;

  GoogleAuthClient(this._headers);

  @override
  Future<StreamedResponse> send(BaseRequest request) {
    request.headers.addAll(_headers);
    return request.send();
  }
}
