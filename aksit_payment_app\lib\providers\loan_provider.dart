import 'package:flutter/foundation.dart';
import '../models/loan.dart';
import '../database/loan_dao.dart';

class LoanProvider with ChangeNotifier {
  final LoanDao _loanDao = LoanDao();
  
  List<Loan> _loans = [];
  List<Map<String, dynamic>> _loansWithDetails = [];
  List<Map<String, dynamic>> _overdueLoans = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Loan> get loans => _loans;
  List<Map<String, dynamic>> get loansWithDetails => _loansWithDetails;
  List<Map<String, dynamic>> get overdueLoans => _overdueLoans;
  Map<String, dynamic> get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل جميع القروض
  Future<void> loadLoans() async {
    _setLoading(true);
    try {
      _loans = await _loanDao.getAllLoans();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل القروض: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل القروض مع التفاصيل
  Future<void> loadLoansWithDetails() async {
    _setLoading(true);
    try {
      _loansWithDetails = await _loanDao.getLoansWithCustomerDetails();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل تفاصيل القروض: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل القروض المتأخرة
  Future<void> loadOverdueLoans() async {
    _setLoading(true);
    try {
      _overdueLoans = await _loanDao.getOverdueLoans();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل القروض المتأخرة: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل إحصائيات القروض
  Future<void> loadStatistics() async {
    _setLoading(true);
    try {
      _statistics = await _loanDao.getLoansStatistics();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الإحصائيات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // إضافة قرض جديد مع الأقساط
  Future<bool> addLoanWithInstallments(Loan loan) async {
    _setLoading(true);
    try {
      final id = await _loanDao.insertLoanWithInstallments(loan);
      if (id > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في إضافة القرض: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث حالة القرض
  Future<bool> updateLoanStatus(int loanId, LoanStatus status) async {
    _setLoading(true);
    try {
      final result = await _loanDao.updateLoanStatus(loanId, status);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث حالة القرض: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث بيانات القرض
  Future<bool> updateLoan(Loan loan) async {
    _setLoading(true);
    try {
      final result = await _loanDao.updateLoan(loan);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث القرض: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف قرض
  Future<bool> deleteLoan(int loanId) async {
    _setLoading(true);
    try {
      // التحقق من إمكانية الحذف
      final canDelete = await _loanDao.canDeleteLoan(loanId);
      if (!canDelete) {
        _error = 'لا يمكن حذف القرض لوجود مدفوعات مرتبطة به';
        _setLoading(false);
        return false;
      }

      final result = await _loanDao.deleteLoan(loanId);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في حذف القرض: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على قروض عميل معين
  Future<List<Loan>> getLoansByCustomerId(int customerId) async {
    try {
      return await _loanDao.getLoansByCustomerId(customerId);
    } catch (e) {
      _error = 'خطأ في الحصول على قروض العميل: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على قرض بواسطة ID
  Future<Loan?> getLoanById(int id) async {
    try {
      return await _loanDao.getLoanById(id);
    } catch (e) {
      _error = 'خطأ في الحصول على القرض: ${e.toString()}';
      debugPrint(_error);
      return null;
    }
  }

  // الحصول على المبلغ المدفوع لقرض معين
  Future<double> getPaidAmountForLoan(int loanId) async {
    try {
      return await _loanDao.getPaidAmountForLoan(loanId);
    } catch (e) {
      _error = 'خطأ في حساب المبلغ المدفوع: ${e.toString()}';
      debugPrint(_error);
      return 0.0;
    }
  }

  // الحصول على القروض النشطة
  Future<List<Loan>> getActiveLoans() async {
    try {
      return await _loanDao.getActiveLoans();
    } catch (e) {
      _error = 'خطأ في الحصول على القروض النشطة: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تحديث البيانات
  Future<void> refresh() async {
    await Future.wait([
      loadLoans(),
      loadLoansWithDetails(),
      loadOverdueLoans(),
      loadStatistics(),
    ]);
  }
}
