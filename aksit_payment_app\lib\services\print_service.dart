import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import '../models/invoice.dart';
import '../models/voucher.dart';
import '../models/customer.dart';
import '../models/invoice_item.dart';

class PrintService {
  static const String _companyName = 'شركة أقسط للمدفوعات';
  static const String _companyAddress = 'العراق - بغداد';
  static const String _companyPhone = '+964 XXX XXX XXXX';

  // طباعة الفاتورة
  static Future<void> printInvoice(
    Invoice invoice,
    Customer customer,
    List<InvoiceItem> items,
  ) async {
    final pdf = pw.Document();

    // تحميل الخط العربي
    final arabicFont = await _loadArabicFont();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الشركة
              _buildCompanyHeader(),
              
              pw.SizedBox(height: 20),
              
              // معلومات الفاتورة
              _buildInvoiceHeader(invoice, customer),
              
              pw.SizedBox(height: 20),
              
              // جدول العناصر
              _buildItemsTable(items),
              
              pw.SizedBox(height: 20),
              
              // ملخص الفاتورة
              _buildInvoiceSummary(invoice),
              
              pw.Spacer(),
              
              // تذييل الفاتورة
              _buildInvoiceFooter(),
            ],
          );
        },
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
      name: 'فاتورة_${invoice.invoiceNumber}.pdf',
    );
  }

  // طباعة السند
  static Future<void> printVoucher(
    Voucher voucher,
    Customer? customer,
  ) async {
    final pdf = pw.Document();
    final arabicFont = await _loadArabicFont();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a5,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الشركة
              _buildCompanyHeader(),
              
              pw.SizedBox(height: 20),
              
              // نوع السند
              pw.Center(
                child: pw.Text(
                  voucher.type == VoucherType.receipt ? 'سند قبض' : 'سند دفع',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // معلومات السند
              _buildVoucherDetails(voucher, customer),
              
              pw.Spacer(),
              
              // التوقيعات
              _buildVoucherSignatures(),
            ],
          );
        },
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
      name: 'سند_${voucher.voucherNumber}.pdf',
    );
  }

  // طباعة كشف الحساب
  static Future<void> printAccountStatement(
    Customer customer,
    List<Map<String, dynamic>> transactions,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final pdf = pw.Document();
    final arabicFont = await _loadArabicFont();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الشركة
              _buildCompanyHeader(),
              
              pw.SizedBox(height: 20),
              
              // عنوان كشف الحساب
              pw.Center(
                child: pw.Text(
                  'كشف حساب',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // معلومات العميل والفترة
              _buildStatementHeader(customer, startDate, endDate),
              
              pw.SizedBox(height: 20),
              
              // جدول المعاملات
              _buildTransactionsTable(transactions),
              
              pw.SizedBox(height: 20),
              
              // ملخص الحساب
              _buildAccountSummary(transactions),
            ],
          );
        },
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
      name: 'كشف_حساب_${customer.name}.pdf',
    );
  }

  // تحميل الخط العربي
  static Future<pw.Font> _loadArabicFont() async {
    // يمكن استخدام خط عربي مخصص هنا
    // في الوقت الحالي سنستخدم الخط الافتراضي
    return pw.Font.ttf(
      await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf'),
    );
  }

  // رأس الشركة
  static pw.Widget _buildCompanyHeader() {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(10),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            _companyName,
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(_companyAddress),
          pw.Text(_companyPhone),
        ],
      ),
    );
  }

  // رأس الفاتورة
  static pw.Widget _buildInvoiceHeader(Invoice invoice, Customer customer) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('رقم الفاتورة: ${invoice.invoiceNumber}'),
            pw.Text('التاريخ: ${_formatDate(invoice.invoiceDate)}'),
            pw.Text('طريقة الدفع: ${invoice.paymentMethodText}'),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('العميل: ${customer.name}'),
            pw.Text('الهاتف: ${customer.phone}'),
            pw.Text('العنوان: ${customer.address}'),
          ],
        ),
      ],
    );
  }

  // جدول عناصر الفاتورة
  static pw.Widget _buildItemsTable(List<InvoiceItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: [
            _buildTableCell('المنتج', isHeader: true),
            _buildTableCell('الكمية', isHeader: true),
            _buildTableCell('الوحدة', isHeader: true),
            _buildTableCell('السعر', isHeader: true),
            _buildTableCell('الإجمالي', isHeader: true),
          ],
        ),
        // صفوف البيانات
        ...items.map((item) => pw.TableRow(
          children: [
            _buildTableCell(item.productName),
            _buildTableCell(item.quantity.toString()),
            _buildTableCell(item.unit),
            _buildTableCell(_formatCurrency(item.unitPrice)),
            _buildTableCell(_formatCurrency(item.totalPrice)),
          ],
        )),
      ],
    );
  }

  // ملخص الفاتورة
  static pw.Widget _buildInvoiceSummary(Invoice invoice) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        children: [
          _buildSummaryRow('إجمالي المبلغ:', _formatCurrency(invoice.totalAmount)),
          _buildSummaryRow('المبلغ المدفوع:', _formatCurrency(invoice.paidAmount)),
          _buildSummaryRow('المبلغ المتبقي:', _formatCurrency(invoice.remainingAmount)),
          if (invoice.paymentMethod == PaymentMethod.installment)
            _buildSummaryRow('القسط الشهري:', _formatCurrency(invoice.monthlyInstallment ?? 0)),
        ],
      ),
    );
  }

  // تذييل الفاتورة
  static pw.Widget _buildInvoiceFooter() {
    return pw.Column(
      children: [
        pw.Text('شكراً لتعاملكم معنا'),
        pw.SizedBox(height: 10),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text('توقيع العميل: _______________'),
            pw.Text('توقيع المندوب: _______________'),
          ],
        ),
      ],
    );
  }

  // تفاصيل السند
  static pw.Widget _buildVoucherDetails(Voucher voucher, Customer? customer) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text('رقم السند: ${voucher.voucherNumber}'),
        pw.Text('التاريخ: ${_formatDate(voucher.voucherDate)}'),
        pw.Text('المبلغ: ${_formatCurrency(voucher.amount)}'),
        if (customer != null) pw.Text('العميل: ${customer.name}'),
        pw.SizedBox(height: 10),
        pw.Text('الوصف: ${voucher.description}'),
        if (voucher.notes != null) pw.Text('ملاحظات: ${voucher.notes}'),
      ],
    );
  }

  // توقيعات السند
  static pw.Widget _buildVoucherSignatures() {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text('المستلم: _______________'),
        pw.Text('المسلم: _______________'),
      ],
    );
  }

  // رأس كشف الحساب
  static pw.Widget _buildStatementHeader(Customer customer, DateTime startDate, DateTime endDate) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text('العميل: ${customer.name}'),
        pw.Text('الهاتف: ${customer.phone}'),
        pw.Text('الفترة: من ${_formatDate(startDate)} إلى ${_formatDate(endDate)}'),
      ],
    );
  }

  // جدول المعاملات
  static pw.Widget _buildTransactionsTable(List<Map<String, dynamic>> transactions) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: [
            _buildTableCell('التاريخ', isHeader: true),
            _buildTableCell('الوصف', isHeader: true),
            _buildTableCell('مدين', isHeader: true),
            _buildTableCell('دائن', isHeader: true),
          ],
        ),
        // صفوف البيانات
        ...transactions.map((transaction) => pw.TableRow(
          children: [
            _buildTableCell(_formatDate(DateTime.parse(transaction['date']))),
            _buildTableCell(transaction['description']),
            _buildTableCell(transaction['type'] == 'debit' ? _formatCurrency(transaction['amount']) : ''),
            _buildTableCell(transaction['type'] == 'credit' ? _formatCurrency(transaction['amount']) : ''),
          ],
        )),
      ],
    );
  }

  // ملخص الحساب
  static pw.Widget _buildAccountSummary(List<Map<String, dynamic>> transactions) {
    double totalDebit = 0;
    double totalCredit = 0;

    for (var transaction in transactions) {
      if (transaction['type'] == 'debit') {
        totalDebit += transaction['amount'] as double;
      } else {
        totalCredit += transaction['amount'] as double;
      }
    }

    final balance = totalDebit - totalCredit;

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        children: [
          _buildSummaryRow('إجمالي المديونية:', _formatCurrency(totalDebit)),
          _buildSummaryRow('إجمالي المدفوع:', _formatCurrency(totalCredit)),
          _buildSummaryRow('الرصيد:', _formatCurrency(balance.abs())),
        ],
      ),
    );
  }

  // خلية الجدول
  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // صف الملخص
  static pw.Widget _buildSummaryRow(String label, String value) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(label),
        pw.Text(value, style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
      ],
    );
  }

  // تنسيق التاريخ
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // تنسيق العملة
  static String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0)} د.ع';
  }
}
