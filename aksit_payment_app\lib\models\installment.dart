enum InstallmentStatus {
  pending, // في الانتظار
  paid, // مدفوع
  overdue, // متأخر
  partial, // مدفوع جزئياً
}

class Installment {
  final int? id;
  final int invoiceId;
  final int installmentNumber;
  final double amount;
  final DateTime dueDate;
  final DateTime? paidDate;
  final double paidAmount;
  final InstallmentStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Installment({
    this.id,
    required this.invoiceId,
    required this.installmentNumber,
    required this.amount,
    required this.dueDate,
    this.paidDate,
    this.paidAmount = 0.0,
    this.status = InstallmentStatus.pending,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى Installment
  factory Installment.fromMap(Map<String, dynamic> map) {
    return Installment(
      id: map['id']?.toInt(),
      invoiceId: map['invoice_id']?.toInt() ?? 0,
      installmentNumber: map['installment_number']?.toInt() ?? 0,
      amount: map['amount']?.toDouble() ?? 0.0,
      dueDate: DateTime.parse(map['due_date']),
      paidDate:
          map['paid_date'] != null ? DateTime.parse(map['paid_date']) : null,
      paidAmount: map['paid_amount']?.toDouble() ?? 0.0,
      status: InstallmentStatus.values[map['status'] ?? 0],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من Installment إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'installment_number': installmentNumber,
      'amount': amount,
      'due_date': dueDate.toIso8601String(),
      'paid_date': paidDate?.toIso8601String(),
      'paid_amount': paidAmount,
      'status': status.index,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // حساب المبلغ المتبقي
  double get remainingAmount {
    return amount - paidAmount;
  }

  // التحقق من التأخير
  bool get isOverdue {
    if (status == InstallmentStatus.paid) return false;
    return DateTime.now().isAfter(dueDate);
  }

  // التحقق من الدفع الكامل
  bool get isFullyPaid {
    return paidAmount >= amount;
  }

  // التحقق من الدفع الجزئي
  bool get isPartiallyPaid {
    return paidAmount > 0 && paidAmount < amount;
  }

  // عدد الأيام المتأخرة
  int get daysOverdue {
    if (!isOverdue) return 0;
    return DateTime.now().difference(dueDate).inDays;
  }

  // إنشاء نسخة محدثة من القسط
  Installment copyWith({
    int? id,
    int? invoiceId,
    int? installmentNumber,
    double? amount,
    DateTime? dueDate,
    DateTime? paidDate,
    double? paidAmount,
    InstallmentStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Installment(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      installmentNumber: installmentNumber ?? this.installmentNumber,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      paidDate: paidDate ?? this.paidDate,
      paidAmount: paidAmount ?? this.paidAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Installment(id: $id, invoiceId: $invoiceId, installmentNumber: $installmentNumber, amount: $amount, dueDate: $dueDate, paidDate: $paidDate, paidAmount: $paidAmount, status: $status, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Installment &&
        other.id == id &&
        other.invoiceId == invoiceId &&
        other.installmentNumber == installmentNumber &&
        other.amount == amount &&
        other.dueDate == dueDate &&
        other.paidDate == paidDate &&
        other.paidAmount == paidAmount &&
        other.status == status &&
        other.notes == notes &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        invoiceId.hashCode ^
        installmentNumber.hashCode ^
        amount.hashCode ^
        dueDate.hashCode ^
        paidDate.hashCode ^
        paidAmount.hashCode ^
        status.hashCode ^
        notes.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
