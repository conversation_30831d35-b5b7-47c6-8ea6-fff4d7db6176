import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/settings_provider.dart';
import 'backup_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الإعدادات')),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // إعدادات التطبيق
              _buildSectionTitle(context, 'إعدادات التطبيق'),
              _buildAppSettings(context, settingsProvider),

              const SizedBox(height: 24),

              // إعدادات العملة والتنسيق
              _buildSectionTitle(context, 'العملة والتنسيق'),
              _buildCurrencySettings(context, settingsProvider),

              const SizedBox(height: 24),

              // النسخ الاحتياطي
              _buildSectionTitle(context, 'النسخ الاحتياطي'),
              _buildBackupSettings(context),

              const SizedBox(height: 24),

              // إعدادات الإشعارات
              _buildSectionTitle(context, 'الإشعارات'),
              _buildNotificationSettings(context, settingsProvider),

              const SizedBox(height: 24),

              // حول التطبيق
              _buildSectionTitle(context, 'حول التطبيق'),
              _buildAboutSettings(context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildAppSettings(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('الوضع الليلي'),
            subtitle: const Text('تفعيل المظهر الداكن'),
            value: settingsProvider.isDarkMode,
            onChanged: (value) {
              settingsProvider.setDarkMode(value);
            },
            secondary: const Icon(Icons.dark_mode),
          ),

          const Divider(height: 1),

          ListTile(
            title: const Text('اللغة'),
            subtitle: Text(
              settingsProvider.language == 'ar' ? 'العربية' : 'English',
            ),
            leading: const Icon(Icons.language),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showLanguageDialog(context, settingsProvider),
          ),

          const Divider(height: 1),

          SwitchListTile(
            title: const Text('الاهتزاز'),
            subtitle: const Text('تفعيل الاهتزاز عند الإجراءات'),
            value: settingsProvider.vibrationEnabled,
            onChanged: (value) {
              settingsProvider.setVibrationEnabled(value);
            },
            secondary: const Icon(Icons.vibration),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencySettings(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) {
    return Card(
      child: Column(
        children: [
          ListTile(
            title: const Text('العملة'),
            subtitle: Text(settingsProvider.currency),
            leading: const Icon(Icons.attach_money),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showCurrencyDialog(context, settingsProvider),
          ),

          const Divider(height: 1),

          SwitchListTile(
            title: const Text('إظهار رمز العملة'),
            subtitle: const Text('عرض رمز العملة مع المبالغ'),
            value: settingsProvider.showCurrencySymbol,
            onChanged: (value) {
              settingsProvider.setShowCurrencySymbol(value);
            },
            secondary: const Icon(Icons.currency_exchange),
          ),

          const Divider(height: 1),

          ListTile(
            title: const Text('تنسيق الأرقام'),
            subtitle: Text(settingsProvider.numberFormat),
            leading: const Icon(Icons.format_list_numbered),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showNumberFormatDialog(context, settingsProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildBackupSettings(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            title: const Text('النسخ الاحتياطي'),
            subtitle: const Text('إدارة النسخ الاحتياطية واستعادة البيانات'),
            leading: const Icon(Icons.backup),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const BackupScreen()),
              );
            },
          ),

          const Divider(height: 1),

          ListTile(
            title: const Text('تصدير البيانات'),
            subtitle: const Text('تصدير البيانات إلى ملف Excel'),
            leading: const Icon(Icons.file_download),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _exportData(context),
          ),

          const Divider(height: 1),

          ListTile(
            title: const Text('مسح البيانات'),
            subtitle: const Text('حذف جميع البيانات من التطبيق'),
            leading: const Icon(Icons.delete_forever, color: Colors.red),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showClearDataDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('إشعارات الأقساط'),
            subtitle: const Text('تذكير بمواعيد استحقاق الأقساط'),
            value: settingsProvider.installmentNotifications,
            onChanged: (value) {
              settingsProvider.setInstallmentNotifications(value);
            },
            secondary: const Icon(Icons.notifications),
          ),

          const Divider(height: 1),

          SwitchListTile(
            title: const Text('إشعارات المتأخرين'),
            subtitle: const Text('تنبيه عند وجود عملاء متأخرين في السداد'),
            value: settingsProvider.overdueNotifications,
            onChanged: (value) {
              settingsProvider.setOverdueNotifications(value);
            },
            secondary: const Icon(Icons.warning),
          ),

          const Divider(height: 1),

          ListTile(
            title: const Text('وقت التذكير'),
            subtitle: Text(
              '${settingsProvider.reminderTime} أيام قبل الاستحقاق',
            ),
            leading: const Icon(Icons.schedule),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showReminderTimeDialog(context, settingsProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSettings(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            title: const Text('إصدار التطبيق'),
            subtitle: const Text('1.0.0'),
            leading: const Icon(Icons.info),
          ),

          const Divider(height: 1),

          ListTile(
            title: const Text('المطور'),
            subtitle: const Text('شركة أقسط للمدفوعات'),
            leading: const Icon(Icons.code),
          ),

          const Divider(height: 1),

          ListTile(
            title: const Text('الدعم الفني'),
            subtitle: const Text('للمساعدة والاستفسارات'),
            leading: const Icon(Icons.support),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _contactSupport(context),
          ),

          const Divider(height: 1),

          ListTile(
            title: const Text('تقييم التطبيق'),
            subtitle: const Text('ساعدنا بتقييم التطبيق'),
            leading: const Icon(Icons.star),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _rateApp(context),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر اللغة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<String>(
                title: const Text('العربية'),
                value: 'ar',
                groupValue: settingsProvider.language,
                onChanged: (value) {
                  settingsProvider.setLanguage(value!);
                  Navigator.of(context).pop();
                },
              ),
              RadioListTile<String>(
                title: const Text('English'),
                value: 'en',
                groupValue: settingsProvider.language,
                onChanged: (value) {
                  settingsProvider.setLanguage(value!);
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showCurrencyDialog(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر العملة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<String>(
                title: const Text('دينار عراقي (د.ع)'),
                value: 'IQD',
                groupValue: settingsProvider.currency,
                onChanged: (value) {
                  settingsProvider.setCurrency(value!);
                  Navigator.of(context).pop();
                },
              ),
              RadioListTile<String>(
                title: const Text('دولار أمريكي (\$)'),
                value: 'USD',
                groupValue: settingsProvider.currency,
                onChanged: (value) {
                  settingsProvider.setCurrency(value!);
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showNumberFormatDialog(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تنسيق الأرقام'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<String>(
                title: const Text('1,234,567'),
                value: 'comma',
                groupValue: settingsProvider.numberFormat,
                onChanged: (value) {
                  settingsProvider.setNumberFormat(value!);
                  Navigator.of(context).pop();
                },
              ),
              RadioListTile<String>(
                title: const Text('1.234.567'),
                value: 'dot',
                groupValue: settingsProvider.numberFormat,
                onChanged: (value) {
                  settingsProvider.setNumberFormat(value!);
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showReminderTimeDialog(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('وقت التذكير'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children:
                [1, 3, 7, 14, 30].map((days) {
                  return RadioListTile<int>(
                    title: Text('$days ${days == 1 ? 'يوم' : 'أيام'}'),
                    value: days,
                    groupValue: settingsProvider.reminderTime,
                    onChanged: (value) {
                      settingsProvider.setReminderTime(value!);
                      Navigator.of(context).pop();
                    },
                  );
                }).toList(),
          ),
        );
      },
    );
  }

  void _exportData(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير البيانات - قيد التطوير')),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تحذير'),
          content: const Text(
            'هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearAllData(context);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  void _clearAllData(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('مسح البيانات - قيد التطوير')));
  }

  void _contactSupport(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('الدعم الفني - قيد التطوير')));
  }

  void _rateApp(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقييم التطبيق - قيد التطوير')),
    );
  }
}
