import 'package:flutter/material.dart';

class ProfitReportScreen extends StatelessWidget {
  const ProfitReportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير الأرباح'),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('طباعة تقرير الأرباح - قيد التطوير')),
              );
            },
          ),
        ],
      ),
      body: const Center(
        child: Text(
          'تقرير الأرباح - قيد التطوير',
          style: TextStyle(fontSize: 18),
        ),
      ),
    );
  }
}
