import 'package:flutter/foundation.dart';
import '../models/invoice.dart';
import '../models/invoice_item.dart';
import '../database/invoice_dao.dart';

class InvoiceProvider with ChangeNotifier {
  final InvoiceDao _invoiceDao = InvoiceDao();
  
  List<Invoice> _invoices = [];
  List<Map<String, dynamic>> _invoicesWithDetails = [];
  List<Map<String, dynamic>> _overdueInvoices = [];
  List<Map<String, dynamic>> _topCustomers = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Invoice> get invoices => _invoices;
  List<Map<String, dynamic>> get invoicesWithDetails => _invoicesWithDetails;
  List<Map<String, dynamic>> get overdueInvoices => _overdueInvoices;
  List<Map<String, dynamic>> get topCustomers => _topCustomers;
  Map<String, dynamic> get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل جميع الفواتير
  Future<void> loadInvoices() async {
    _setLoading(true);
    try {
      _invoices = await _invoiceDao.getAllInvoices();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الفواتير: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل الفواتير مع التفاصيل
  Future<void> loadInvoicesWithDetails() async {
    _setLoading(true);
    try {
      _invoicesWithDetails = await _invoiceDao.getInvoicesWithCustomerDetails();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل تفاصيل الفواتير: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل الفواتير المتأخرة
  Future<void> loadOverdueInvoices() async {
    _setLoading(true);
    try {
      _overdueInvoices = await _invoiceDao.getOverdueInvoices();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الفواتير المتأخرة: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل أفضل العملاء
  Future<void> loadTopCustomers({int limit = 10}) async {
    _setLoading(true);
    try {
      _topCustomers = await _invoiceDao.getTopCustomers(limit: limit);
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل أفضل العملاء: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل إحصائيات الفواتير
  Future<void> loadStatistics() async {
    _setLoading(true);
    try {
      _statistics = await _invoiceDao.getInvoicesStatistics();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الإحصائيات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // إضافة فاتورة جديدة مع العناصر
  Future<bool> addInvoiceWithItems(
    Invoice invoice,
    List<InvoiceItem> items,
  ) async {
    _setLoading(true);
    try {
      final id = await _invoiceDao.insertInvoiceWithItems(invoice, items);
      if (id > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في إضافة الفاتورة: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث حالة الفاتورة
  Future<bool> updateInvoiceStatus(int invoiceId, InvoiceStatus status) async {
    _setLoading(true);
    try {
      final result = await _invoiceDao.updateInvoiceStatus(invoiceId, status);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث حالة الفاتورة: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث المبلغ المدفوع
  Future<bool> updateInvoicePayment(
    int invoiceId,
    double paidAmount,
    double remainingAmount,
  ) async {
    _setLoading(true);
    try {
      final result = await _invoiceDao.updateInvoicePayment(
        invoiceId,
        paidAmount,
        remainingAmount,
      );
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث المدفوعات: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث بيانات الفاتورة
  Future<bool> updateInvoice(Invoice invoice) async {
    _setLoading(true);
    try {
      final result = await _invoiceDao.updateInvoice(invoice);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث الفاتورة: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف فاتورة
  Future<bool> deleteInvoice(int invoiceId) async {
    _setLoading(true);
    try {
      final result = await _invoiceDao.deleteInvoice(invoiceId);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في حذف الفاتورة: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على فواتير عميل معين
  Future<List<Invoice>> getInvoicesByCustomerId(int customerId) async {
    try {
      return await _invoiceDao.getInvoicesByCustomerId(customerId);
    } catch (e) {
      _error = 'خطأ في الحصول على فواتير العميل: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على فاتورة بواسطة ID
  Future<Invoice?> getInvoiceById(int id) async {
    try {
      return await _invoiceDao.getInvoiceById(id);
    } catch (e) {
      _error = 'خطأ في الحصول على الفاتورة: ${e.toString()}';
      debugPrint(_error);
      return null;
    }
  }

  // الحصول على عناصر فاتورة
  Future<List<InvoiceItem>> getInvoiceItems(int invoiceId) async {
    try {
      return await _invoiceDao.getInvoiceItems(invoiceId);
    } catch (e) {
      _error = 'خطأ في الحصول على عناصر الفاتورة: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على تقرير المبيعات
  Future<Map<String, dynamic>> getSalesReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _invoiceDao.getSalesReport(startDate, endDate);
    } catch (e) {
      _error = 'خطأ في الحصول على تقرير المبيعات: ${e.toString()}';
      debugPrint(_error);
      return {};
    }
  }

  // توليد رقم فاتورة جديد
  String generateInvoiceNumber() {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    final time = now.millisecondsSinceEpoch.toString().substring(8);
    
    return 'INV-$year$month$day-$time';
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تحديث البيانات
  Future<void> refresh() async {
    await Future.wait([
      loadInvoices(),
      loadInvoicesWithDetails(),
      loadOverdueInvoices(),
      loadTopCustomers(),
      loadStatistics(),
    ]);
  }
}
