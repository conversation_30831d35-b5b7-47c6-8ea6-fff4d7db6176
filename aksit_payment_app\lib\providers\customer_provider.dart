import 'package:flutter/foundation.dart';
import '../models/customer.dart';
import '../database/customer_dao.dart';

class CustomerProvider with ChangeNotifier {
  final CustomerDao _customerDao = CustomerDao();
  
  List<Customer> _customers = [];
  List<Map<String, dynamic>> _customersWithDebt = [];
  List<Map<String, dynamic>> _overdueCustomers = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Customer> get customers => _customers;
  List<Map<String, dynamic>> get customersWithDebt => _customersWithDebt;
  List<Map<String, dynamic>> get overdueCustomers => _overdueCustomers;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل جميع العملاء
  Future<void> loadCustomers() async {
    _setLoading(true);
    try {
      _customers = await _customerDao.getAllCustomers();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل العملاء: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل العملاء مع المديونية
  Future<void> loadCustomersWithDebt() async {
    _setLoading(true);
    try {
      _customersWithDebt = await _customerDao.getCustomersWithDebt();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل بيانات المديونية: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل العملاء المتأخرين
  Future<void> loadOverdueCustomers() async {
    _setLoading(true);
    try {
      _overdueCustomers = await _customerDao.getOverdueCustomers();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل العملاء المتأخرين: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // إضافة عميل جديد
  Future<bool> addCustomer(Customer customer) async {
    _setLoading(true);
    try {
      final id = await _customerDao.insertCustomer(customer);
      if (id > 0) {
        await loadCustomers();
        await loadCustomersWithDebt();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في إضافة العميل: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث بيانات العميل
  Future<bool> updateCustomer(Customer customer) async {
    _setLoading(true);
    try {
      final result = await _customerDao.updateCustomer(customer);
      if (result > 0) {
        await loadCustomers();
        await loadCustomersWithDebt();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث العميل: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف عميل
  Future<bool> deleteCustomer(int customerId) async {
    _setLoading(true);
    try {
      final result = await _customerDao.deleteCustomer(customerId);
      if (result > 0) {
        await loadCustomers();
        await loadCustomersWithDebt();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في حذف العميل: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // البحث عن العملاء
  Future<List<Customer>> searchCustomers(String query) async {
    try {
      if (query.isEmpty) {
        return _customers;
      }
      return await _customerDao.searchCustomers(query);
    } catch (e) {
      _error = 'خطأ في البحث: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على عميل بواسطة ID
  Future<Customer?> getCustomerById(int id) async {
    try {
      return await _customerDao.getCustomerById(id);
    } catch (e) {
      _error = 'خطأ في الحصول على العميل: ${e.toString()}';
      debugPrint(_error);
      return null;
    }
  }

  // الحصول على عدد العملاء
  Future<int> getCustomersCount() async {
    try {
      return await _customerDao.getCustomersCount();
    } catch (e) {
      _error = 'خطأ في الحصول على عدد العملاء: ${e.toString()}';
      debugPrint(_error);
      return 0;
    }
  }

  // استعادة عميل محذوف
  Future<bool> restoreCustomer(int customerId) async {
    _setLoading(true);
    try {
      final result = await _customerDao.restoreCustomer(customerId);
      if (result > 0) {
        await loadCustomers();
        await loadCustomersWithDebt();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في استعادة العميل: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على العملاء المحذوفين
  Future<List<Customer>> getDeletedCustomers() async {
    try {
      return await _customerDao.getDeletedCustomers();
    } catch (e) {
      _error = 'خطأ في الحصول على العملاء المحذوفين: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تحديث البيانات
  Future<void> refresh() async {
    await Future.wait([
      loadCustomers(),
      loadCustomersWithDebt(),
      loadOverdueCustomers(),
    ]);
  }
}
