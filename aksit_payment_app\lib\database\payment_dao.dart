import 'package:sqflite/sqflite.dart';
import '../models/payment.dart';
import 'database_helper.dart';

class PaymentDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة دفعة جديدة
  Future<int> insertPayment(Payment payment) async {
    final db = await _databaseHelper.database;
    return await db.insert('payments', payment.toMap());
  }

  // الحصول على جميع المدفوعات
  Future<List<Payment>> getAllPayments() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      orderBy: 'payment_date DESC',
    );

    return List.generate(maps.length, (i) {
      return Payment.fromMap(maps[i]);
    });
  }

  // الحصول على مدفوعات قسط معين
  Future<List<Payment>> getPaymentsByInstallmentId(int installmentId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'installment_id = ?',
      whereArgs: [installmentId],
      orderBy: 'payment_date DESC',
    );

    return List.generate(maps.length, (i) {
      return Payment.fromMap(maps[i]);
    });
  }

  // الحصول على مدفوعات عميل معين
  Future<List<Map<String, dynamic>>> getPaymentsByCustomerId(int customerId) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        p.*,
        i.installment_number,
        i.due_date,
        l.id as loan_id,
        c.name as customer_name
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.id = ? AND c.is_active = 1
      ORDER BY p.payment_date DESC
    ''', [customerId]);

    return result;
  }

  // الحصول على دفعة بواسطة ID
  Future<Payment?> getPaymentById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Payment.fromMap(maps.first);
    }
    return null;
  }

  // تحديث دفعة
  Future<int> updatePayment(Payment payment) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'payments',
      payment.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [payment.id],
    );
  }

  // حذف دفعة
  Future<int> deletePayment(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على المدفوعات في فترة زمنية معينة
  Future<List<Map<String, dynamic>>> getPaymentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        p.*,
        i.installment_number,
        i.due_date,
        l.id as loan_id,
        c.name as customer_name,
        c.phone as customer_phone
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1
        AND date(p.payment_date) >= date(?)
        AND date(p.payment_date) <= date(?)
      ORDER BY p.payment_date DESC
    ''', [startDate.toIso8601String(), endDate.toIso8601String()]);

    return result;
  }

  // الحصول على إحصائيات المدفوعات
  Future<Map<String, dynamic>> getPaymentsStatistics() async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_payments,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        MIN(amount) as min_amount,
        MAX(amount) as max_amount
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1
    ''');

    return result.first;
  }

  // الحصول على المدفوعات اليومية
  Future<List<Map<String, dynamic>>> getTodaysPayments() async {
    final db = await _databaseHelper.database;
    final String today = DateTime.now().toIso8601String().split('T')[0];
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        p.*,
        i.installment_number,
        i.due_date,
        l.id as loan_id,
        c.name as customer_name,
        c.phone as customer_phone
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1
        AND date(p.payment_date) = '$today'
      ORDER BY p.payment_date DESC
    ''');

    return result;
  }

  // الحصول على المدفوعات الشهرية
  Future<List<Map<String, dynamic>>> getMonthlyPayments(int year, int month) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        p.*,
        i.installment_number,
        i.due_date,
        l.id as loan_id,
        c.name as customer_name,
        c.phone as customer_phone
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1
        AND strftime('%Y', p.payment_date) = ?
        AND strftime('%m', p.payment_date) = ?
      ORDER BY p.payment_date DESC
    ''', [year.toString(), month.toString().padLeft(2, '0')]);

    return result;
  }

  // الحصول على إجمالي المدفوعات لفترة معينة
  Future<double> getTotalPaymentsForPeriod(DateTime startDate, DateTime endDate) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT COALESCE(SUM(p.amount), 0) as total
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1
        AND date(p.payment_date) >= date(?)
        AND date(p.payment_date) <= date(?)
    ''', [startDate.toIso8601String(), endDate.toIso8601String()]);

    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  // البحث في المدفوعات
  Future<List<Map<String, dynamic>>> searchPayments(String query) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        p.*,
        i.installment_number,
        i.due_date,
        l.id as loan_id,
        c.name as customer_name,
        c.phone as customer_phone
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1
        AND (c.name LIKE ? OR c.phone LIKE ? OR p.receipt_number LIKE ? OR p.notes LIKE ?)
      ORDER BY p.payment_date DESC
    ''', ['%$query%', '%$query%', '%$query%', '%$query%']);

    return result;
  }

  // الحصول على آخر المدفوعات
  Future<List<Map<String, dynamic>>> getRecentPayments(int limit) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        p.*,
        i.installment_number,
        i.due_date,
        l.id as loan_id,
        c.name as customer_name,
        c.phone as customer_phone
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1
      ORDER BY p.payment_date DESC
      LIMIT ?
    ''', [limit]);

    return result;
  }
}
