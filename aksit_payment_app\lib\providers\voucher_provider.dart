import 'package:flutter/foundation.dart';
import '../models/voucher.dart';
import '../database/voucher_dao.dart';

class VoucherProvider with ChangeNotifier {
  final VoucherDao _voucherDao = VoucherDao();
  
  List<Voucher> _vouchers = [];
  List<Map<String, dynamic>> _vouchersWithDetails = [];
  List<Map<String, dynamic>> _recentVouchers = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Voucher> get vouchers => _vouchers;
  List<Map<String, dynamic>> get vouchersWithDetails => _vouchersWithDetails;
  List<Map<String, dynamic>> get recentVouchers => _recentVouchers;
  Map<String, dynamic> get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل جميع السندات
  Future<void> loadVouchers() async {
    _setLoading(true);
    try {
      _vouchers = await _voucherDao.getAllVouchers();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل السندات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل السندات مع التفاصيل
  Future<void> loadVouchersWithDetails() async {
    _setLoading(true);
    try {
      _vouchersWithDetails = await _voucherDao.getVouchersWithCustomerDetails();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل تفاصيل السندات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل آخر السندات
  Future<void> loadRecentVouchers({int limit = 10}) async {
    _setLoading(true);
    try {
      _recentVouchers = await _voucherDao.getRecentVouchers(limit: limit);
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل آخر السندات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل إحصائيات السندات
  Future<void> loadStatistics() async {
    _setLoading(true);
    try {
      _statistics = await _voucherDao.getVouchersStatistics();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الإحصائيات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // إضافة سند جديد
  Future<bool> addVoucher(Voucher voucher) async {
    _setLoading(true);
    try {
      final id = await _voucherDao.insertVoucher(voucher);
      if (id > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في إضافة السند: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث حالة السند
  Future<bool> updateVoucherStatus(int voucherId, VoucherStatus status) async {
    _setLoading(true);
    try {
      final result = await _voucherDao.updateVoucherStatus(voucherId, status);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث حالة السند: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث بيانات السند
  Future<bool> updateVoucher(Voucher voucher) async {
    _setLoading(true);
    try {
      final result = await _voucherDao.updateVoucher(voucher);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث السند: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف سند
  Future<bool> deleteVoucher(int voucherId) async {
    _setLoading(true);
    try {
      final result = await _voucherDao.deleteVoucher(voucherId);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في حذف السند: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على سندات بنوع معين
  Future<List<Voucher>> getVouchersByType(VoucherType type) async {
    try {
      return await _voucherDao.getVouchersByType(type);
    } catch (e) {
      _error = 'خطأ في الحصول على السندات: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على سندات عميل معين
  Future<List<Voucher>> getVouchersByCustomerId(int customerId) async {
    try {
      return await _voucherDao.getVouchersByCustomerId(customerId);
    } catch (e) {
      _error = 'خطأ في الحصول على سندات العميل: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على سندات فاتورة معينة
  Future<List<Voucher>> getVouchersByInvoiceId(int invoiceId) async {
    try {
      return await _voucherDao.getVouchersByInvoiceId(invoiceId);
    } catch (e) {
      _error = 'خطأ في الحصول على سندات الفاتورة: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على سند بواسطة ID
  Future<Voucher?> getVoucherById(int id) async {
    try {
      return await _voucherDao.getVoucherById(id);
    } catch (e) {
      _error = 'خطأ في الحصول على السند: ${e.toString()}';
      debugPrint(_error);
      return null;
    }
  }

  // البحث في السندات
  Future<List<Map<String, dynamic>>> searchVouchers(String query) async {
    try {
      return await _voucherDao.searchVouchers(query);
    } catch (e) {
      _error = 'خطأ في البحث: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على السندات لفترة معينة
  Future<List<Voucher>> getVouchersByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _voucherDao.getVouchersByDateRange(startDate, endDate);
    } catch (e) {
      _error = 'خطأ في الحصول على السندات: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // الحصول على تقرير السندات
  Future<Map<String, dynamic>> getVouchersReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _voucherDao.getVouchersReport(startDate, endDate);
    } catch (e) {
      _error = 'خطأ في الحصول على تقرير السندات: ${e.toString()}';
      debugPrint(_error);
      return {};
    }
  }

  // تطبيق فلتر على السندات
  Future<List<Map<String, dynamic>>> getFilteredVouchers(VoucherFilter filter) async {
    try {
      return await _voucherDao.getFilteredVouchers(filter);
    } catch (e) {
      _error = 'خطأ في تطبيق الفلتر: ${e.toString()}';
      debugPrint(_error);
      return [];
    }
  }

  // توليد رقم سند جديد
  String generateVoucherNumber(VoucherType type) {
    return VoucherNumberGenerator.generateVoucherNumber(type);
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تحديث البيانات
  Future<void> refresh() async {
    await Future.wait([
      loadVouchers(),
      loadVouchersWithDetails(),
      loadRecentVouchers(),
      loadStatistics(),
    ]);
  }
}
