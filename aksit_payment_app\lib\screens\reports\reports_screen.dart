import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/voucher_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/settings_provider.dart';
import '../../widgets/report_card.dart';
import 'sales_report_screen.dart';
import 'customer_report_screen.dart';
import 'financial_report_screen.dart';
import 'profit_report_screen.dart';
import 'account_statement_screen.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadReportsData();
    });
  }

  Future<void> _loadReportsData() async {
    final invoiceProvider = Provider.of<InvoiceProvider>(
      context,
      listen: false,
    );
    final voucherProvider = Provider.of<VoucherProvider>(
      context,
      listen: false,
    );
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      invoiceProvider.loadStatistics(),
      voucherProvider.loadStatistics(),
      customerProvider.loadCustomersWithDebt(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReportsData,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadReportsData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ملخص سريع
              _buildQuickSummary(),

              const SizedBox(height: 24),

              // تقارير المبيعات
              _buildSectionTitle('تقارير المبيعات'),
              const SizedBox(height: 16),
              _buildSalesReports(),

              const SizedBox(height: 24),

              // تقارير العملاء
              _buildSectionTitle('تقارير العملاء'),
              const SizedBox(height: 16),
              _buildCustomerReports(),

              const SizedBox(height: 24),

              // التقارير المالية
              _buildSectionTitle('التقارير المالية'),
              const SizedBox(height: 16),
              _buildFinancialReports(),

              const SizedBox(height: 24),

              // تقارير أخرى
              _buildSectionTitle('تقارير أخرى'),
              const SizedBox(height: 16),
              _buildOtherReports(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickSummary() {
    return Consumer3<InvoiceProvider, VoucherProvider, CustomerProvider>(
      builder: (
        context,
        invoiceProvider,
        voucherProvider,
        customerProvider,
        child,
      ) {
        final invoiceStats = invoiceProvider.statistics;
        final voucherStats = voucherProvider.statistics;
        final settingsProvider = Provider.of<SettingsProvider>(context);

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ملخص سريع',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryItem(
                        'إجمالي المبيعات',
                        settingsProvider.formatCurrency(
                          (invoiceStats['total_sales'] as num?)?.toDouble() ??
                              0,
                        ),
                        Icons.trending_up,
                        Colors.blue,
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        'إجمالي الأرباح',
                        settingsProvider.formatCurrency(
                          (invoiceStats['total_profits'] as num?)?.toDouble() ??
                              0,
                        ),
                        Icons.monetization_on,
                        Colors.green,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryItem(
                        'سندات القبض',
                        settingsProvider.formatCurrency(
                          (voucherStats['total_receipts'] as num?)
                                  ?.toDouble() ??
                              0,
                        ),
                        Icons.receipt,
                        Colors.purple,
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        'سندات الدفع',
                        settingsProvider.formatCurrency(
                          (voucherStats['total_payments'] as num?)
                                  ?.toDouble() ??
                              0,
                        ),
                        Icons.payment,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
    );
  }

  Widget _buildSalesReports() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildReportCard(
          'تقرير المبيعات',
          'تقرير شامل للمبيعات',
          Icons.bar_chart,
          Colors.blue,
          () => _navigateToSalesReport(),
        ),
        _buildReportCard(
          'تقرير الأرباح',
          'تحليل الأرباح والخسائر',
          Icons.trending_up,
          Colors.green,
          () => _navigateToProfitReport(),
        ),
        _buildReportCard(
          'الفواتير المتأخرة',
          'الفواتير غير المسددة',
          Icons.warning,
          Colors.orange,
          () => _showOverdueInvoices(),
        ),
        _buildReportCard(
          'أفضل المنتجات',
          'المنتجات الأكثر مبيعاً',
          Icons.star,
          Colors.purple,
          () => _showTopProducts(),
        ),
      ],
    );
  }

  Widget _buildCustomerReports() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildReportCard(
          'تقرير العملاء',
          'تحليل بيانات العملاء',
          Icons.people,
          Colors.indigo,
          () => _navigateToCustomerReport(),
        ),
        _buildReportCard(
          'أفضل العملاء',
          'العملاء الأكثر شراءً',
          Icons.person_pin,
          Colors.teal,
          () => _showTopCustomers(),
        ),
        _buildReportCard(
          'العملاء المتأخرين',
          'العملاء المتأخرين في السداد',
          Icons.person_off,
          Colors.red,
          () => _showOverdueCustomers(),
        ),
        _buildReportCard(
          'كشف الحساب',
          'كشف حساب العملاء',
          Icons.account_balance,
          Colors.brown,
          () => _navigateToAccountStatement(),
        ),
      ],
    );
  }

  Widget _buildFinancialReports() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildReportCard(
          'التقرير المالي',
          'الوضع المالي الشامل',
          Icons.account_balance_wallet,
          Colors.deepPurple,
          () => _navigateToFinancialReport(),
        ),
        _buildReportCard(
          'تقرير السندات',
          'سندات القبض والدفع',
          Icons.description,
          Colors.cyan,
          () => _showVouchersReport(),
        ),
        _buildReportCard(
          'التدفق النقدي',
          'تحليل التدفق النقدي',
          Icons.waterfall_chart,
          Colors.lightGreen,
          () => _showCashFlowReport(),
        ),
        _buildReportCard(
          'الميزانية',
          'ميزانية الشركة',
          Icons.balance,
          Colors.amber,
          () => _showBalanceSheet(),
        ),
      ],
    );
  }

  Widget _buildOtherReports() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildReportCard(
          'تقرير الأقساط',
          'حالة الأقساط والمدفوعات',
          Icons.schedule,
          Colors.pink,
          () => _showInstallmentsReport(),
        ),
        _buildReportCard(
          'تقرير المخزون',
          'حالة المخزون والمنتجات',
          Icons.inventory,
          Colors.grey,
          () => _showInventoryReport(),
        ),
        _buildReportCard(
          'تقرير مخصص',
          'إنشاء تقرير مخصص',
          Icons.build,
          Colors.blueGrey,
          () => _createCustomReport(),
        ),
        _buildReportCard(
          'طباعة التقارير',
          'طباعة وتصدير التقارير',
          Icons.print,
          Colors.black87,
          () => _showPrintOptions(),
        ),
      ],
    );
  }

  Widget _buildReportCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 40, color: color),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Navigation methods
  void _navigateToSalesReport() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const SalesReportScreen()));
  }

  void _navigateToProfitReport() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ProfitReportScreen()));
  }

  void _navigateToCustomerReport() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CustomerReportScreen()),
    );
  }

  void _navigateToFinancialReport() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const FinancialReportScreen()),
    );
  }

  void _navigateToAccountStatement() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AccountStatementScreen()),
    );
  }

  // Show methods for other reports
  void _showOverdueInvoices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الفواتير المتأخرة - قيد التطوير')),
    );
  }

  void _showTopProducts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('أفضل المنتجات - قيد التطوير')),
    );
  }

  void _showTopCustomers() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('أفضل العملاء - قيد التطوير')));
  }

  void _showOverdueCustomers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('العملاء المتأخرين - قيد التطوير')),
    );
  }

  void _showVouchersReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير السندات - قيد التطوير')),
    );
  }

  void _showCashFlowReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التدفق النقدي - قيد التطوير')),
    );
  }

  void _showBalanceSheet() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('الميزانية - قيد التطوير')));
  }

  void _showInstallmentsReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير الأقساط - قيد التطوير')),
    );
  }

  void _showInventoryReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير المخزون - قيد التطوير')),
    );
  }

  void _createCustomReport() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تقرير مخصص - قيد التطوير')));
  }

  void _showPrintOptions() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('خيارات الطباعة - قيد التطوير')),
    );
  }
}
