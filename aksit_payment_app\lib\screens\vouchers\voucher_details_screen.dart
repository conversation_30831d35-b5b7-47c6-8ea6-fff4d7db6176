import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/voucher.dart';
import '../../models/customer.dart';
import '../../models/invoice.dart';
import '../../providers/voucher_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/settings_provider.dart';
import 'add_voucher_screen.dart';

class VoucherDetailsScreen extends StatefulWidget {
  final Voucher voucher;

  const VoucherDetailsScreen({super.key, required this.voucher});

  @override
  State<VoucherDetailsScreen> createState() => _VoucherDetailsScreenState();
}

class _VoucherDetailsScreenState extends State<VoucherDetailsScreen> {
  Customer? _customer;
  Invoice? _invoice;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final customerProvider = Provider.of<CustomerProvider>(context, listen: false);
      final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
      
      if (widget.voucher.customerId != null) {
        _customer = await customerProvider.getCustomerById(widget.voucher.customerId!);
      }
      
      if (widget.voucher.invoiceId != null) {
        _invoice = await invoiceProvider.getInvoiceById(widget.voucher.invoiceId!);
      }
      
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('سند ${widget.voucher.voucherNumber}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareVoucher,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editVoucher();
                  break;
                case 'confirm':
                  _changeStatus(VoucherStatus.confirmed);
                  break;
                case 'cancel':
                  _changeStatus(VoucherStatus.cancelled);
                  break;
                case 'print':
                  _printVoucher();
                  break;
                case 'delete':
                  _deleteVoucher();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              if (widget.voucher.status == VoucherStatus.draft)
                const PopupMenuItem(
                  value: 'confirm',
                  child: Row(
                    children: [
                      Icon(Icons.check, color: Colors.green),
                      SizedBox(width: 8),
                      Text('تأكيد'),
                    ],
                  ),
                ),
              if (widget.voucher.status != VoucherStatus.cancelled)
                const PopupMenuItem(
                  value: 'cancel',
                  child: Row(
                    children: [
                      Icon(Icons.cancel, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('إلغاء'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print),
                    SizedBox(width: 8),
                    Text('طباعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات السند الأساسية
                  _buildVoucherHeader(settingsProvider),
                  
                  const SizedBox(height: 24),
                  
                  // معلومات العميل
                  if (_customer != null) _buildCustomerInfo(),
                  
                  const SizedBox(height: 24),
                  
                  // معلومات الفاتورة
                  if (_invoice != null) _buildInvoiceInfo(settingsProvider),
                  
                  const SizedBox(height: 24),
                  
                  // تفاصيل إضافية
                  _buildAdditionalDetails(),
                ],
              ),
            ),
    );
  }

  Widget _buildVoucherHeader(SettingsProvider settingsProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getTypeColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getTypeIcon(),
                    color: _getTypeColor(),
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.voucher.typeText,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: _getTypeColor(),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        widget.voucher.voucherNumber,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    widget.voucher.statusText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // المبلغ
            Center(
              child: Column(
                children: [
                  Text(
                    'المبلغ',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    settingsProvider.formatCurrency(widget.voucher.amount),
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: _getTypeColor(),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            _buildInfoRow('التاريخ', _formatDate(widget.voucher.voucherDate)),
            _buildInfoRow('الوصف', widget.voucher.description),
            
            if (widget.voucher.notes != null && widget.voucher.notes!.isNotEmpty)
              _buildInfoRow('ملاحظات', widget.voucher.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العميل',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('الاسم', _customer!.name),
            _buildInfoRow('الهاتف', _customer!.phone),
            _buildInfoRow('العنوان', _customer!.address),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceInfo(SettingsProvider settingsProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('رقم الفاتورة', _invoice!.invoiceNumber),
            _buildInfoRow('تاريخ الفاتورة', _formatDate(_invoice!.invoiceDate)),
            _buildInfoRow('إجمالي الفاتورة', settingsProvider.formatCurrency(_invoice!.totalAmount)),
            _buildInfoRow('المبلغ المتبقي', settingsProvider.formatCurrency(_invoice!.remainingAmount)),
            _buildInfoRow('حالة الفاتورة', _invoice!.statusText),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل إضافية',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('تاريخ الإنشاء', _formatDateTime(widget.voucher.createdAt)),
            _buildInfoRow('آخر تحديث', _formatDateTime(widget.voucher.updatedAt)),
            
            if (widget.voucher.attachmentPath != null)
              _buildInfoRow('المرفق', widget.voucher.attachmentPath!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor() {
    switch (widget.voucher.type) {
      case VoucherType.receipt:
        return Colors.green;
      case VoucherType.payment:
        return Colors.red;
    }
  }

  IconData _getTypeIcon() {
    switch (widget.voucher.type) {
      case VoucherType.receipt:
        return Icons.receipt;
      case VoucherType.payment:
        return Icons.payment;
    }
  }

  Color _getStatusColor() {
    switch (widget.voucher.status) {
      case VoucherStatus.draft:
        return Colors.orange;
      case VoucherStatus.confirmed:
        return Colors.green;
      case VoucherStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _shareVoucher() {
    // سيتم تطبيقها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة السند - قيد التطوير')),
    );
  }

  void _editVoucher() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddVoucherScreen(voucher: widget.voucher),
      ),
    ).then((_) => _loadData());
  }

  void _changeStatus(VoucherStatus status) async {
    final voucherProvider = Provider.of<VoucherProvider>(context, listen: false);
    final success = await voucherProvider.updateVoucherStatus(widget.voucher.id!, status);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success 
                ? 'تم تحديث حالة السند بنجاح' 
                : 'فشل في تحديث حالة السند',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
      
      if (success) {
        Navigator.of(context).pop();
      }
    }
  }

  void _printVoucher() {
    // سيتم تطبيقها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('طباعة السند - قيد التطوير')),
    );
  }

  void _deleteVoucher() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف السند "${widget.voucher.voucherNumber}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                final voucherProvider = Provider.of<VoucherProvider>(
                  context,
                  listen: false,
                );
                
                navigator.pop(); // إغلاق الحوار
                
                final success = await voucherProvider.deleteVoucher(widget.voucher.id!);

                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        success ? 'تم حذف السند بنجاح' : 'فشل في حذف السند',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                  
                  if (success) {
                    navigator.pop(); // العودة إلى الشاشة السابقة
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
