enum PaymentType {
  installment, // دفعة قسط
  partial,     // دفعة جزئية
  advance,     // دفعة مقدمة
  penalty      // غرامة تأخير
}

class Payment {
  final int? id;
  final int installmentId;
  final double amount;
  final DateTime paymentDate;
  final PaymentType type;
  final String? notes;
  final String? receiptNumber;
  final DateTime createdAt;
  final DateTime updatedAt;

  Payment({
    this.id,
    required this.installmentId,
    required this.amount,
    required this.paymentDate,
    this.type = PaymentType.installment,
    this.notes,
    this.receiptNumber,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى Payment
  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      id: map['id']?.toInt(),
      installmentId: map['installment_id']?.toInt() ?? 0,
      amount: map['amount']?.toDouble() ?? 0.0,
      paymentDate: DateTime.parse(map['payment_date']),
      type: PaymentType.values[map['type'] ?? 0],
      notes: map['notes'],
      receiptNumber: map['receipt_number'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من Payment إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'installment_id': installmentId,
      'amount': amount,
      'payment_date': paymentDate.toIso8601String(),
      'type': type.index,
      'notes': notes,
      'receipt_number': receiptNumber,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // الحصول على نص نوع الدفعة بالعربية
  String get typeText {
    switch (type) {
      case PaymentType.installment:
        return 'دفعة قسط';
      case PaymentType.partial:
        return 'دفعة جزئية';
      case PaymentType.advance:
        return 'دفعة مقدمة';
      case PaymentType.penalty:
        return 'غرامة تأخير';
    }
  }

  // إنشاء نسخة محدثة من الدفعة
  Payment copyWith({
    int? id,
    int? installmentId,
    double? amount,
    DateTime? paymentDate,
    PaymentType? type,
    String? notes,
    String? receiptNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Payment(
      id: id ?? this.id,
      installmentId: installmentId ?? this.installmentId,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      receiptNumber: receiptNumber ?? this.receiptNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Payment(id: $id, installmentId: $installmentId, amount: $amount, paymentDate: $paymentDate, type: $type, notes: $notes, receiptNumber: $receiptNumber, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Payment &&
        other.id == id &&
        other.installmentId == installmentId &&
        other.amount == amount &&
        other.paymentDate == paymentDate &&
        other.type == type &&
        other.notes == notes &&
        other.receiptNumber == receiptNumber &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        installmentId.hashCode ^
        amount.hashCode ^
        paymentDate.hashCode ^
        type.hashCode ^
        notes.hashCode ^
        receiptNumber.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
