import 'package:flutter/foundation.dart';
import '../models/installment.dart';
import '../database/installment_dao.dart';

class InstallmentProvider with ChangeNotifier {
  final InstallmentDao _installmentDao = InstallmentDao();
  
  List<Installment> _installments = [];
  List<Map<String, dynamic>> _overdueInstallments = [];
  List<Map<String, dynamic>> _todaysDueInstallments = [];
  List<Map<String, dynamic>> _upcomingInstallments = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Installment> get installments => _installments;
  List<Map<String, dynamic>> get overdueInstallments => _overdueInstallments;
  List<Map<String, dynamic>> get todaysDueInstallments => _todaysDueInstallments;
  List<Map<String, dynamic>> get upcomingInstallments => _upcomingInstallments;
  Map<String, dynamic> get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل أقساط قرض معين
  Future<void> loadInstallmentsByLoanId(int loanId) async {
    _setLoading(true);
    try {
      _installments = await _installmentDao.getInstallmentsByLoanId(loanId);
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الأقساط: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل الأقساط المتأخرة
  Future<void> loadOverdueInstallments() async {
    _setLoading(true);
    try {
      _overdueInstallments = await _installmentDao.getOverdueInstallments();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الأقساط المتأخرة: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل الأقساط المستحقة اليوم
  Future<void> loadTodaysDueInstallments() async {
    _setLoading(true);
    try {
      _todaysDueInstallments = await _installmentDao.getTodaysDueInstallments();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل أقساط اليوم: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل الأقساط القادمة
  Future<void> loadUpcomingInstallments(int days) async {
    _setLoading(true);
    try {
      _upcomingInstallments = await _installmentDao.getUpcomingInstallments(days);
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الأقساط القادمة: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تحميل إحصائيات الأقساط
  Future<void> loadStatistics() async {
    _setLoading(true);
    try {
      _statistics = await _installmentDao.getInstallmentsStatistics();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الإحصائيات: ${e.toString()}';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // تسديد قسط
  Future<bool> payInstallment(int installmentId, double amount, {String? notes}) async {
    _setLoading(true);
    try {
      final result = await _installmentDao.payInstallment(installmentId, amount, notes: notes);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تسديد القسط: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // إلغاء دفعة قسط
  Future<bool> cancelInstallmentPayment(int installmentId, double amount) async {
    _setLoading(true);
    try {
      final result = await _installmentDao.cancelInstallmentPayment(installmentId, amount);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في إلغاء الدفعة: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث قسط
  Future<bool> updateInstallment(Installment installment) async {
    _setLoading(true);
    try {
      final result = await _installmentDao.updateInstallment(installment);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في تحديث القسط: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // حذف قسط
  Future<bool> deleteInstallment(int installmentId) async {
    _setLoading(true);
    try {
      final result = await _installmentDao.deleteInstallment(installmentId);
      if (result > 0) {
        await refresh();
        _error = null;
        return true;
      }
      return false;
    } catch (e) {
      _error = 'خطأ في حذف القسط: ${e.toString()}';
      debugPrint(_error);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على قسط بواسطة ID
  Future<Installment?> getInstallmentById(int id) async {
    try {
      return await _installmentDao.getInstallmentById(id);
    } catch (e) {
      _error = 'خطأ في الحصول على القسط: ${e.toString()}';
      debugPrint(_error);
      return null;
    }
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تحديث البيانات
  Future<void> refresh() async {
    await Future.wait([
      loadOverdueInstallments(),
      loadTodaysDueInstallments(),
      loadUpcomingInstallments(7), // الأقساط القادمة خلال 7 أيام
      loadStatistics(),
    ]);
  }

  // تحديث بيانات قرض معين
  Future<void> refreshLoanInstallments(int loanId) async {
    await loadInstallmentsByLoanId(loanId);
  }
}
