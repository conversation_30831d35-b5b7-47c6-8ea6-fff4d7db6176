class InvoiceItem {
  final int? id;
  final int invoiceId;
  final String productName;
  final String? productDescription;
  final double quantity;
  final String unit;
  final double unitCost;
  final double unitPrice;
  final double totalCost;
  final double totalPrice;
  final double profit;
  final DateTime createdAt;
  final DateTime updatedAt;

  InvoiceItem({
    this.id,
    required this.invoiceId,
    required this.productName,
    this.productDescription,
    required this.quantity,
    this.unit = 'قطعة',
    required this.unitCost,
    required this.unitPrice,
    required this.totalCost,
    required this.totalPrice,
    required this.profit,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى InvoiceItem
  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      id: map['id']?.toInt(),
      invoiceId: map['invoice_id']?.toInt() ?? 0,
      productName: map['product_name'] ?? '',
      productDescription: map['product_description'],
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unit: map['unit'] ?? 'قطعة',
      unitCost: map['unit_cost']?.toDouble() ?? 0.0,
      unitPrice: map['unit_price']?.toDouble() ?? 0.0,
      totalCost: map['total_cost']?.toDouble() ?? 0.0,
      totalPrice: map['total_price']?.toDouble() ?? 0.0,
      profit: map['profit']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من InvoiceItem إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'product_name': productName,
      'product_description': productDescription,
      'quantity': quantity,
      'unit': unit,
      'unit_cost': unitCost,
      'unit_price': unitPrice,
      'total_cost': totalCost,
      'total_price': totalPrice,
      'profit': profit,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // حساب نسبة الربح
  double get profitPercentage {
    if (totalPrice == 0) return 0;
    return (profit / totalPrice) * 100;
  }

  // حساب نسبة الربح على التكلفة
  double get profitMargin {
    if (totalCost == 0) return 0;
    return (profit / totalCost) * 100;
  }

  // إنشاء عنصر فاتورة من البيانات الأساسية
  factory InvoiceItem.create({
    required int invoiceId,
    required String productName,
    String? productDescription,
    required double quantity,
    String unit = 'قطعة',
    required double unitCost,
    required double unitPrice,
  }) {
    final totalCost = quantity * unitCost;
    final totalPrice = quantity * unitPrice;
    final profit = totalPrice - totalCost;

    return InvoiceItem(
      invoiceId: invoiceId,
      productName: productName,
      productDescription: productDescription,
      quantity: quantity,
      unit: unit,
      unitCost: unitCost,
      unitPrice: unitPrice,
      totalCost: totalCost,
      totalPrice: totalPrice,
      profit: profit,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // إنشاء نسخة محدثة من العنصر
  InvoiceItem copyWith({
    int? id,
    int? invoiceId,
    String? productName,
    String? productDescription,
    double? quantity,
    String? unit,
    double? unitCost,
    double? unitPrice,
    double? totalCost,
    double? totalPrice,
    double? profit,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      productName: productName ?? this.productName,
      productDescription: productDescription ?? this.productDescription,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      unitCost: unitCost ?? this.unitCost,
      unitPrice: unitPrice ?? this.unitPrice,
      totalCost: totalCost ?? this.totalCost,
      totalPrice: totalPrice ?? this.totalPrice,
      profit: profit ?? this.profit,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // تحديث الحسابات عند تغيير الكمية أو الأسعار
  InvoiceItem updateCalculations({
    double? newQuantity,
    double? newUnitCost,
    double? newUnitPrice,
  }) {
    final qty = newQuantity ?? quantity;
    final cost = newUnitCost ?? unitCost;
    final price = newUnitPrice ?? unitPrice;
    
    final newTotalCost = qty * cost;
    final newTotalPrice = qty * price;
    final newProfit = newTotalPrice - newTotalCost;

    return copyWith(
      quantity: qty,
      unitCost: cost,
      unitPrice: price,
      totalCost: newTotalCost,
      totalPrice: newTotalPrice,
      profit: newProfit,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'InvoiceItem(id: $id, productName: $productName, quantity: $quantity, totalPrice: $totalPrice, profit: $profit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceItem &&
        other.id == id &&
        other.invoiceId == invoiceId &&
        other.productName == productName &&
        other.quantity == quantity &&
        other.unitPrice == unitPrice;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        invoiceId.hashCode ^
        productName.hashCode ^
        quantity.hashCode ^
        unitPrice.hashCode;
  }
}
