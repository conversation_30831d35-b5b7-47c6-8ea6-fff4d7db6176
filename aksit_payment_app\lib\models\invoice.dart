enum InvoiceStatus {
  draft,     // مسودة
  active,    // نشطة
  completed, // مكتملة
  cancelled  // ملغية
}

enum PaymentMethod {
  cash,        // نقدي
  installment, // تقسيط
  mixed        // مختلط
}

class Invoice {
  final int? id;
  final String invoiceNumber;
  final int customerId;
  final DateTime invoiceDate;
  final double totalAmount;
  final double costAmount;
  final double profitAmount;
  final double paidAmount;
  final double remainingAmount;
  final PaymentMethod paymentMethod;
  final int? installmentMonths;
  final double? monthlyInstallment;
  final InvoiceStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Invoice({
    this.id,
    required this.invoiceNumber,
    required this.customerId,
    required this.invoiceDate,
    required this.totalAmount,
    required this.costAmount,
    required this.profitAmount,
    this.paidAmount = 0.0,
    required this.remainingAmount,
    required this.paymentMethod,
    this.installmentMonths,
    this.monthlyInstallment,
    this.status = InvoiceStatus.draft,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى Invoice
  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id']?.toInt(),
      invoiceNumber: map['invoice_number'] ?? '',
      customerId: map['customer_id']?.toInt() ?? 0,
      invoiceDate: DateTime.parse(map['invoice_date']),
      totalAmount: map['total_amount']?.toDouble() ?? 0.0,
      costAmount: map['cost_amount']?.toDouble() ?? 0.0,
      profitAmount: map['profit_amount']?.toDouble() ?? 0.0,
      paidAmount: map['paid_amount']?.toDouble() ?? 0.0,
      remainingAmount: map['remaining_amount']?.toDouble() ?? 0.0,
      paymentMethod: PaymentMethod.values[map['payment_method'] ?? 0],
      installmentMonths: map['installment_months']?.toInt(),
      monthlyInstallment: map['monthly_installment']?.toDouble(),
      status: InvoiceStatus.values[map['status'] ?? 0],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من Invoice إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'customer_id': customerId,
      'invoice_date': invoiceDate.toIso8601String(),
      'total_amount': totalAmount,
      'cost_amount': costAmount,
      'profit_amount': profitAmount,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'payment_method': paymentMethod.index,
      'installment_months': installmentMonths,
      'monthly_installment': monthlyInstallment,
      'status': status.index,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // حساب نسبة الربح
  double get profitPercentage {
    if (totalAmount == 0) return 0;
    return (profitAmount / totalAmount) * 100;
  }

  // حساب نسبة المدفوع
  double get paidPercentage {
    if (totalAmount == 0) return 0;
    return (paidAmount / totalAmount) * 100;
  }

  // التحقق من اكتمال الدفع
  bool get isFullyPaid {
    return remainingAmount <= 0;
  }

  // التحقق من وجود تأخير
  bool get hasOverduePayments {
    // سيتم تطبيق المنطق مع الأقساط
    return false;
  }

  // الحصول على نص حالة الفاتورة
  String get statusText {
    switch (status) {
      case InvoiceStatus.draft:
        return 'مسودة';
      case InvoiceStatus.active:
        return 'نشطة';
      case InvoiceStatus.completed:
        return 'مكتملة';
      case InvoiceStatus.cancelled:
        return 'ملغية';
    }
  }

  // الحصول على نص طريقة الدفع
  String get paymentMethodText {
    switch (paymentMethod) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.installment:
        return 'تقسيط';
      case PaymentMethod.mixed:
        return 'مختلط';
    }
  }

  // إنشاء نسخة محدثة من الفاتورة
  Invoice copyWith({
    int? id,
    String? invoiceNumber,
    int? customerId,
    DateTime? invoiceDate,
    double? totalAmount,
    double? costAmount,
    double? profitAmount,
    double? paidAmount,
    double? remainingAmount,
    PaymentMethod? paymentMethod,
    int? installmentMonths,
    double? monthlyInstallment,
    InvoiceStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      invoiceDate: invoiceDate ?? this.invoiceDate,
      totalAmount: totalAmount ?? this.totalAmount,
      costAmount: costAmount ?? this.costAmount,
      profitAmount: profitAmount ?? this.profitAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      installmentMonths: installmentMonths ?? this.installmentMonths,
      monthlyInstallment: monthlyInstallment ?? this.monthlyInstallment,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Invoice(id: $id, invoiceNumber: $invoiceNumber, customerId: $customerId, totalAmount: $totalAmount, profitAmount: $profitAmount, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Invoice &&
        other.id == id &&
        other.invoiceNumber == invoiceNumber &&
        other.customerId == customerId &&
        other.totalAmount == totalAmount &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        invoiceNumber.hashCode ^
        customerId.hashCode ^
        totalAmount.hashCode ^
        status.hashCode;
  }
}
