import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/customer.dart';
import '../../models/invoice.dart';
import '../../models/invoice_item.dart';
import '../../providers/customer_provider.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/settings_provider.dart';

class AddInvoiceScreen extends StatefulWidget {
  const AddInvoiceScreen({super.key});

  @override
  State<AddInvoiceScreen> createState() => _AddInvoiceScreenState();
}

class _AddInvoiceScreenState extends State<AddInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  
  Customer? _selectedCustomer;
  PaymentMethod _paymentMethod = PaymentMethod.cash;
  int? _installmentMonths;
  List<InvoiceItem> _items = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false).loadCustomers();
    });
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء فاتورة جديدة'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveInvoice,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text(
                    'حفظ',
                    style: TextStyle(color: Colors.white),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // اختيار العميل
              _buildCustomerSelection(),
              
              const SizedBox(height: 24),
              
              // طريقة الدفع
              _buildPaymentMethodSelection(),
              
              const SizedBox(height: 24),
              
              // عناصر الفاتورة
              _buildInvoiceItems(),
              
              const SizedBox(height: 24),
              
              // ملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
              ),
              
              const SizedBox(height: 24),
              
              // ملخص الفاتورة
              _buildInvoiceSummary(),
              
              const SizedBox(height: 32),
              
              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveInvoice,
                      child: _isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text('حفظ الفاتورة'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addInvoiceItem,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCustomerSelection() {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العميل *',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<Customer>(
              value: _selectedCustomer,
              decoration: const InputDecoration(
                prefixIcon: Icon(Icons.person),
                hintText: 'اختر العميل',
              ),
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار العميل';
                }
                return null;
              },
              items: customerProvider.customers.map((customer) {
                return DropdownMenuItem<Customer>(
                  value: customer,
                  child: Text('${customer.name} - ${customer.phone}'),
                );
              }).toList(),
              onChanged: (customer) {
                setState(() {
                  _selectedCustomer = customer;
                });
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildPaymentMethodSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'طريقة الدفع',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<PaymentMethod>(
                title: const Text('نقدي'),
                value: PaymentMethod.cash,
                groupValue: _paymentMethod,
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                    _installmentMonths = null;
                  });
                },
              ),
            ),
            Expanded(
              child: RadioListTile<PaymentMethod>(
                title: const Text('تقسيط'),
                value: PaymentMethod.installment,
                groupValue: _paymentMethod,
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                  });
                },
              ),
            ),
          ],
        ),
        
        // عدد الأقساط
        if (_paymentMethod == PaymentMethod.installment) ...[
          const SizedBox(height: 16),
          DropdownButtonFormField<int>(
            value: _installmentMonths,
            decoration: const InputDecoration(
              labelText: 'عدد الأقساط (شهر)',
              prefixIcon: Icon(Icons.schedule),
            ),
            validator: (value) {
              if (_paymentMethod == PaymentMethod.installment && value == null) {
                return 'يرجى اختيار عدد الأقساط';
              }
              return null;
            },
            items: [3, 6, 9, 12, 18, 24, 36].map((months) {
              return DropdownMenuItem<int>(
                value: months,
                child: Text('$months شهر'),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _installmentMonths = value;
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildInvoiceItems() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'عناصر الفاتورة',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            TextButton.icon(
              onPressed: _addInvoiceItem,
              icon: const Icon(Icons.add),
              label: const Text('إضافة عنصر'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        if (_items.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.inventory_2_outlined,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 8),
                Text(
                  'لا توجد عناصر في الفاتورة',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: _addInvoiceItem,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة عنصر'),
                ),
              ],
            ),
          )
        else
          ...List.generate(_items.length, (index) {
            final item = _items[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                title: Text(item.productName),
                subtitle: Text(
                  '${item.quantity} ${item.unit} × ${Provider.of<SettingsProvider>(context).formatCurrency(item.unitPrice)}',
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      Provider.of<SettingsProvider>(context).formatCurrency(item.totalPrice),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () {
                        setState(() {
                          _items.removeAt(index);
                        });
                      },
                    ),
                  ],
                ),
              ),
            );
          }),
      ],
    );
  }

  Widget _buildInvoiceSummary() {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final totalCost = _items.fold<double>(0, (sum, item) => sum + item.totalCost);
    final totalPrice = _items.fold<double>(0, (sum, item) => sum + item.totalPrice);
    final totalProfit = totalPrice - totalCost;
    final profitPercentage = totalPrice > 0 ? (totalProfit / totalPrice) * 100 : 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الفاتورة',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي التكلفة:'),
                Text(
                  settingsProvider.formatCurrency(totalCost),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي السعر:'),
                Text(
                  settingsProvider.formatCurrency(totalPrice),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي الربح:'),
                Text(
                  settingsProvider.formatCurrency(totalProfit),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: totalProfit >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('نسبة الربح:'),
                Text(
                  '${profitPercentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: profitPercentage >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            
            if (_paymentMethod == PaymentMethod.installment && _installmentMonths != null) ...[
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('القسط الشهري:'),
                  Text(
                    settingsProvider.formatCurrency(totalPrice / _installmentMonths!),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _addInvoiceItem() {
    showDialog(
      context: context,
      builder: (context) => _InvoiceItemDialog(
        onSave: (item) {
          setState(() {
            _items.add(item);
          });
        },
      ),
    );
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) return;
    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة عنصر واحد على الأقل')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
      final totalCost = _items.fold<double>(0, (sum, item) => sum + item.totalCost);
      final totalPrice = _items.fold<double>(0, (sum, item) => sum + item.totalPrice);
      final totalProfit = totalPrice - totalCost;
      
      final invoice = Invoice(
        invoiceNumber: invoiceProvider.generateInvoiceNumber(),
        customerId: _selectedCustomer!.id!,
        invoiceDate: DateTime.now(),
        totalAmount: totalPrice,
        costAmount: totalCost,
        profitAmount: totalProfit,
        remainingAmount: totalPrice,
        paymentMethod: _paymentMethod,
        installmentMonths: _installmentMonths,
        monthlyInstallment: _paymentMethod == PaymentMethod.installment 
            ? totalPrice / _installmentMonths! 
            : null,
        status: InvoiceStatus.active,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await invoiceProvider.addInvoiceWithItems(invoice, _items);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إنشاء الفاتورة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إنشاء الفاتورة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

class _InvoiceItemDialog extends StatefulWidget {
  final Function(InvoiceItem) onSave;

  const _InvoiceItemDialog({required this.onSave});

  @override
  State<_InvoiceItemDialog> createState() => _InvoiceItemDialogState();
}

class _InvoiceItemDialogState extends State<_InvoiceItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _productNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _quantityController = TextEditingController();
  final _unitController = TextEditingController(text: 'قطعة');
  final _unitCostController = TextEditingController();
  final _unitPriceController = TextEditingController();

  @override
  void dispose() {
    _productNameController.dispose();
    _descriptionController.dispose();
    _quantityController.dispose();
    _unitController.dispose();
    _unitCostController.dispose();
    _unitPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة عنصر جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _productNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المنتج *',
                  prefixIcon: Icon(Icons.inventory_2),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم المنتج';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف (اختياري)',
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: _quantityController,
                      decoration: const InputDecoration(
                        labelText: 'الكمية *',
                        prefixIcon: Icon(Icons.numbers),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'مطلوب';
                        }
                        if (double.tryParse(value) == null || double.parse(value) <= 0) {
                          return 'كمية غير صحيحة';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      controller: _unitController,
                      decoration: const InputDecoration(
                        labelText: 'الوحدة',
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _unitCostController,
                decoration: const InputDecoration(
                  labelText: 'سعر التكلفة *',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال سعر التكلفة';
                  }
                  if (double.tryParse(value) == null || double.parse(value) < 0) {
                    return 'سعر غير صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _unitPriceController,
                decoration: const InputDecoration(
                  labelText: 'سعر البيع *',
                  prefixIcon: Icon(Icons.sell),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال سعر البيع';
                  }
                  if (double.tryParse(value) == null || double.parse(value) <= 0) {
                    return 'سعر غير صحيح';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveItem,
          child: const Text('إضافة'),
        ),
      ],
    );
  }

  void _saveItem() {
    if (!_formKey.currentState!.validate()) return;

    final item = InvoiceItem.create(
      invoiceId: 0, // سيتم تحديثه لاحقاً
      productName: _productNameController.text.trim(),
      productDescription: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      quantity: double.parse(_quantityController.text),
      unit: _unitController.text.trim(),
      unitCost: double.parse(_unitCostController.text),
      unitPrice: double.parse(_unitPriceController.text),
    );

    widget.onSave(item);
    Navigator.of(context).pop();
  }
}
