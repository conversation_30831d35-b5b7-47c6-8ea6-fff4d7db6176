import 'package:sqflite/sqflite.dart';
import '../models/loan.dart';
import '../models/installment.dart';
import 'database_helper.dart';

class LoanDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة قرض جديد مع الأقساط
  Future<int> insertLoanWithInstallments(Loan loan) async {
    final db = await _databaseHelper.database;
    
    return await db.transaction((txn) async {
      // إدراج القرض
      int loanId = await txn.insert('loans', loan.toMap());
      
      // إنشاء الأقساط
      DateTime currentDate = loan.startDate;
      for (int i = 1; i <= loan.numberOfInstallments; i++) {
        final installment = Installment(
          loanId: loanId,
          installmentNumber: i,
          amount: loan.installmentAmount,
          dueDate: currentDate,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await txn.insert('installments', installment.toMap());
        
        // إضافة شهر للقسط التالي
        currentDate = DateTime(
          currentDate.year,
          currentDate.month + 1,
          currentDate.day,
        );
      }
      
      return loanId;
    });
  }

  // الحصول على جميع القروض
  Future<List<Loan>> getAllLoans() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'loans',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Loan.fromMap(maps[i]);
    });
  }

  // الحصول على قروض عميل معين
  Future<List<Loan>> getLoansByCustomerId(int customerId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'loans',
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Loan.fromMap(maps[i]);
    });
  }

  // الحصول على قرض بواسطة ID
  Future<Loan?> getLoanById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'loans',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Loan.fromMap(maps.first);
    }
    return null;
  }

  // تحديث حالة القرض
  Future<int> updateLoanStatus(int loanId, LoanStatus status) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'loans',
      {
        'status': status.index,
        'updated_at': DateTime.now().toIso8601String(),
        if (status == LoanStatus.completed) 'end_date': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [loanId],
    );
  }

  // تحديث بيانات القرض
  Future<int> updateLoan(Loan loan) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'loans',
      loan.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [loan.id],
    );
  }

  // حذف قرض
  Future<int> deleteLoan(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'loans',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على إحصائيات القروض
  Future<Map<String, dynamic>> getLoansStatistics() async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_loans,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as active_loans,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as completed_loans,
        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as overdue_loans,
        SUM(total_amount) as total_amount,
        AVG(total_amount) as average_amount
      FROM loans
    ''');

    return result.first;
  }

  // الحصول على القروض مع تفاصيل العميل
  Future<List<Map<String, dynamic>>> getLoansWithCustomerDetails() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        l.*,
        c.name as customer_name,
        c.phone as customer_phone,
        COALESCE(SUM(i.paid_amount), 0) as total_paid,
        (l.total_amount - COALESCE(SUM(i.paid_amount), 0)) as remaining_amount
      FROM loans l
      INNER JOIN customers c ON l.customer_id = c.id
      LEFT JOIN installments i ON l.id = i.loan_id
      WHERE c.is_active = 1
      GROUP BY l.id
      ORDER BY l.created_at DESC
    ''');

    return result;
  }

  // الحصول على القروض المتأخرة
  Future<List<Map<String, dynamic>>> getOverdueLoans() async {
    final db = await _databaseHelper.database;
    final String today = DateTime.now().toIso8601String().split('T')[0];
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT DISTINCT
        l.*,
        c.name as customer_name,
        c.phone as customer_phone,
        COUNT(i.id) as overdue_installments,
        SUM(i.amount - i.paid_amount) as overdue_amount
      FROM loans l
      INNER JOIN customers c ON l.customer_id = c.id
      INNER JOIN installments i ON l.id = i.loan_id
      WHERE c.is_active = 1 
        AND l.status = 0
        AND i.status != 1 
        AND i.due_date < ?
        AND (i.amount - i.paid_amount) > 0
      GROUP BY l.id
      ORDER BY l.created_at DESC
    ''', [today]);

    return result;
  }

  // حساب المبلغ المدفوع لقرض معين
  Future<double> getPaidAmountForLoan(int loanId) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT COALESCE(SUM(paid_amount), 0) as total_paid
      FROM installments
      WHERE loan_id = ?
    ''', [loanId]);

    return (result.first['total_paid'] as num?)?.toDouble() ?? 0.0;
  }

  // التحقق من إمكانية حذف القرض
  Future<bool> canDeleteLoan(int loanId) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT COUNT(*) as payment_count
      FROM payments p
      INNER JOIN installments i ON p.installment_id = i.id
      WHERE i.loan_id = ?
    ''', [loanId]);

    int paymentCount = Sqflite.firstIntValue(result) ?? 0;
    return paymentCount == 0;
  }

  // الحصول على القروض النشطة فقط
  Future<List<Loan>> getActiveLoans() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'loans',
      where: 'status = ?',
      whereArgs: [LoanStatus.active.index],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Loan.fromMap(maps[i]);
    });
  }
}
