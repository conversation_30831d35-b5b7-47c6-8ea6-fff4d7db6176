enum LoanStatus {
  active,    // نشط
  completed, // مكتمل
  overdue,   // متأخر
  cancelled  // ملغي
}

class Loan {
  final int? id;
  final int customerId;
  final double totalAmount;
  final int numberOfInstallments;
  final double installmentAmount;
  final DateTime startDate;
  final DateTime? endDate;
  final LoanStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Loan({
    this.id,
    required this.customerId,
    required this.totalAmount,
    required this.numberOfInstallments,
    required this.installmentAmount,
    required this.startDate,
    this.endDate,
    this.status = LoanStatus.active,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى Loan
  factory Loan.fromMap(Map<String, dynamic> map) {
    return Loan(
      id: map['id']?.toInt(),
      customerId: map['customer_id']?.toInt() ?? 0,
      totalAmount: map['total_amount']?.toDouble() ?? 0.0,
      numberOfInstallments: map['number_of_installments']?.toInt() ?? 0,
      installmentAmount: map['installment_amount']?.toDouble() ?? 0.0,
      startDate: DateTime.parse(map['start_date']),
      endDate: map['end_date'] != null ? DateTime.parse(map['end_date']) : null,
      status: LoanStatus.values[map['status'] ?? 0],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من Loan إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'total_amount': totalAmount,
      'number_of_installments': numberOfInstallments,
      'installment_amount': installmentAmount,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'status': status.index,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // حساب المبلغ المدفوع
  double get paidAmount {
    // سيتم حسابه من جدول المدفوعات
    return 0.0;
  }

  // حساب المبلغ المتبقي
  double get remainingAmount {
    return totalAmount - paidAmount;
  }

  // التحقق من التأخير
  bool get isOverdue {
    if (status == LoanStatus.completed || status == LoanStatus.cancelled) {
      return false;
    }
    // منطق التحقق من التأخير سيتم تطبيقه لاحقاً
    return false;
  }

  // إنشاء نسخة محدثة من القرض
  Loan copyWith({
    int? id,
    int? customerId,
    double? totalAmount,
    int? numberOfInstallments,
    double? installmentAmount,
    DateTime? startDate,
    DateTime? endDate,
    LoanStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Loan(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      totalAmount: totalAmount ?? this.totalAmount,
      numberOfInstallments: numberOfInstallments ?? this.numberOfInstallments,
      installmentAmount: installmentAmount ?? this.installmentAmount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Loan(id: $id, customerId: $customerId, totalAmount: $totalAmount, numberOfInstallments: $numberOfInstallments, installmentAmount: $installmentAmount, startDate: $startDate, endDate: $endDate, status: $status, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Loan &&
        other.id == id &&
        other.customerId == customerId &&
        other.totalAmount == totalAmount &&
        other.numberOfInstallments == numberOfInstallments &&
        other.installmentAmount == installmentAmount &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.status == status &&
        other.notes == notes &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        totalAmount.hashCode ^
        numberOfInstallments.hashCode ^
        installmentAmount.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        status.hashCode ^
        notes.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
