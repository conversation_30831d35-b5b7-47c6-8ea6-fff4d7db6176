import 'package:sqflite/sqflite.dart';
import '../models/installment.dart';
import '../models/payment.dart';
import 'database_helper.dart';

class InstallmentDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // الحصول على أقساط قرض معين
  Future<List<Installment>> getInstallmentsByLoanId(int loanId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'installments',
      where: 'loan_id = ?',
      whereArgs: [loanId],
      orderBy: 'installment_number ASC',
    );

    return List.generate(maps.length, (i) {
      return Installment.fromMap(maps[i]);
    });
  }

  // الحصول على قسط بواسطة ID
  Future<Installment?> getInstallmentById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'installments',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Installment.fromMap(maps.first);
    }
    return null;
  }

  // تسديد قسط
  Future<int> payInstallment(int installmentId, double amount, {String? notes}) async {
    final db = await _databaseHelper.database;
    
    return await db.transaction((txn) async {
      // الحصول على بيانات القسط الحالية
      final installmentMaps = await txn.query(
        'installments',
        where: 'id = ?',
        whereArgs: [installmentId],
      );
      
      if (installmentMaps.isEmpty) {
        throw Exception('القسط غير موجود');
      }
      
      final installment = Installment.fromMap(installmentMaps.first);
      final newPaidAmount = installment.paidAmount + amount;
      
      // تحديد حالة القسط الجديدة
      InstallmentStatus newStatus;
      if (newPaidAmount >= installment.amount) {
        newStatus = InstallmentStatus.paid;
      } else if (newPaidAmount > 0) {
        newStatus = InstallmentStatus.partial;
      } else {
        newStatus = InstallmentStatus.pending;
      }
      
      // تحديث القسط
      await txn.update(
        'installments',
        {
          'paid_amount': newPaidAmount,
          'paid_date': newStatus == InstallmentStatus.paid ? DateTime.now().toIso8601String() : null,
          'status': newStatus.index,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [installmentId],
      );
      
      // إضافة سجل الدفعة
      final payment = Payment(
        installmentId: installmentId,
        amount: amount,
        paymentDate: DateTime.now(),
        type: PaymentType.installment,
        notes: notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      return await txn.insert('payments', payment.toMap());
    });
  }

  // الحصول على الأقساط المتأخرة
  Future<List<Map<String, dynamic>>> getOverdueInstallments() async {
    final db = await _databaseHelper.database;
    final String today = DateTime.now().toIso8601String().split('T')[0];
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        i.*,
        l.customer_id,
        c.name as customer_name,
        c.phone as customer_phone,
        (i.amount - i.paid_amount) as remaining_amount,
        (julianday('$today') - julianday(i.due_date)) as days_overdue
      FROM installments i
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1 
        AND i.status != 1 
        AND i.due_date < '$today'
        AND (i.amount - i.paid_amount) > 0
      ORDER BY i.due_date ASC
    ''');

    return result;
  }

  // الحصول على الأقساط المستحقة اليوم
  Future<List<Map<String, dynamic>>> getTodaysDueInstallments() async {
    final db = await _databaseHelper.database;
    final String today = DateTime.now().toIso8601String().split('T')[0];
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        i.*,
        l.customer_id,
        c.name as customer_name,
        c.phone as customer_phone,
        (i.amount - i.paid_amount) as remaining_amount
      FROM installments i
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1 
        AND i.status != 1 
        AND date(i.due_date) = '$today'
        AND (i.amount - i.paid_amount) > 0
      ORDER BY c.name ASC
    ''');

    return result;
  }

  // الحصول على الأقساط القادمة
  Future<List<Map<String, dynamic>>> getUpcomingInstallments(int days) async {
    final db = await _databaseHelper.database;
    final String today = DateTime.now().toIso8601String().split('T')[0];
    final String futureDate = DateTime.now().add(Duration(days: days)).toIso8601String().split('T')[0];
    
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        i.*,
        l.customer_id,
        c.name as customer_name,
        c.phone as customer_phone,
        (i.amount - i.paid_amount) as remaining_amount
      FROM installments i
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1 
        AND i.status != 1 
        AND date(i.due_date) > '$today'
        AND date(i.due_date) <= '$futureDate'
        AND (i.amount - i.paid_amount) > 0
      ORDER BY i.due_date ASC
    ''');

    return result;
  }

  // تحديث قسط
  Future<int> updateInstallment(Installment installment) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'installments',
      installment.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [installment.id],
    );
  }

  // حذف قسط
  Future<int> deleteInstallment(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'installments',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على إحصائيات الأقساط
  Future<Map<String, dynamic>> getInstallmentsStatistics() async {
    final db = await _databaseHelper.database;
    final String today = DateTime.now().toIso8601String().split('T')[0];
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_installments,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as paid_installments,
        SUM(CASE WHEN status = 0 AND due_date >= '$today' THEN 1 ELSE 0 END) as pending_installments,
        SUM(CASE WHEN status != 1 AND due_date < '$today' THEN 1 ELSE 0 END) as overdue_installments,
        SUM(amount) as total_amount,
        SUM(paid_amount) as total_paid,
        SUM(amount - paid_amount) as total_remaining
      FROM installments i
      INNER JOIN loans l ON i.loan_id = l.id
      INNER JOIN customers c ON l.customer_id = c.id
      WHERE c.is_active = 1
    ''');

    return result.first;
  }

  // إلغاء دفعة قسط
  Future<int> cancelInstallmentPayment(int installmentId, double amount) async {
    final db = await _databaseHelper.database;
    
    return await db.transaction((txn) async {
      // الحصول على بيانات القسط الحالية
      final installmentMaps = await txn.query(
        'installments',
        where: 'id = ?',
        whereArgs: [installmentId],
      );
      
      if (installmentMaps.isEmpty) {
        throw Exception('القسط غير موجود');
      }
      
      final installment = Installment.fromMap(installmentMaps.first);
      final newPaidAmount = installment.paidAmount - amount;
      
      if (newPaidAmount < 0) {
        throw Exception('لا يمكن إلغاء مبلغ أكبر من المدفوع');
      }
      
      // تحديد حالة القسط الجديدة
      InstallmentStatus newStatus;
      if (newPaidAmount >= installment.amount) {
        newStatus = InstallmentStatus.paid;
      } else if (newPaidAmount > 0) {
        newStatus = InstallmentStatus.partial;
      } else {
        newStatus = InstallmentStatus.pending;
      }
      
      // تحديث القسط
      return await txn.update(
        'installments',
        {
          'paid_amount': newPaidAmount,
          'paid_date': newStatus == InstallmentStatus.paid ? installment.paidDate?.toIso8601String() : null,
          'status': newStatus.index,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [installmentId],
      );
    });
  }
}
